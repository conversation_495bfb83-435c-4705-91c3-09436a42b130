package com.quhong.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.net.*;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class NetworkUtils {
    private static final Logger logger = LoggerFactory.getLogger(NetworkUtils.class);

    public static String searchIP(String localIP) {
        if (StringUtils.isEmpty(localIP)) {
            logger.error("localIP is null. {}", localIP);
            return null;
        }
        HostIP hostIP = new HostIP(localIP);
        if(hostIP.mask == 32){
            // 32位，不再对比
            return hostIP.host;
        }
        return searchIP(hostIP);
    }

    public static int generateInt(String ip) {
        try {
            String[] arr = ip.trim().split("/");
            InetAddress inetAddress = InetAddress.getByName(arr[0]);
            byte[] bytes = inetAddress.getAddress();
            return bytes[2] * 100 + bytes[3];
        } catch (Exception e) {
            logger.error("generate ip error. {}", e.getMessage(), e);
        }
        return 0;
    }

    public static String searchIP(HostIP hostIP) {
        try {
            Enumeration<NetworkInterface> allNetInterfaces = NetworkInterface.getNetworkInterfaces();
            InetAddress ip = null;
            while (allNetInterfaces.hasMoreElements()) {
                NetworkInterface netInterface = allNetInterfaces.nextElement();
                /**
                 * eno1：代表由主板bios内置的网卡
                 * ens1:代表有主板bios内置的PCI-E网卡
                 * enp2s0: PCI-E独立网卡
                 * eth0：如果以上都不使用，则回到默认的网卡名
                 */
                if (netInterface.getName().startsWith("eth") || netInterface.getName().startsWith("en")) {
                    Enumeration<InetAddress> addresses = netInterface.getInetAddresses();
                    while (addresses.hasMoreElements()) {
                        ip = addresses.nextElement();
                        logger.info("search net interface. name={} ip={}", netInterface.getName(), ip);
                        if (ip != null && ip instanceof Inet4Address) {
                            if(hostIP.match(ip)){
                                return ip.getHostAddress();
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("search host ip error. {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 将bytes转换为整数
     * @param srcBytes
     * @return
     */
    public static int addressToInt(byte[] srcBytes){
        int ret = 0;
        for (int i = 0; i < srcBytes.length; i ++) {
            byte value = srcBytes[i];
            ret = ret | (value << (i * 8));
        }
        return ret;
    }

    public static class HostIP {
        private int addressValue;
        private int mask;
        private String host;

        public HostIP(String ip) {
            parseIp(ip);
        }

        private void parseIp(String ip) {
            try {
                String[] arr = ip.trim().split("/");
                InetAddress inetAddress = InetAddress.getByName(arr[0]);
                this.host = inetAddress.getHostAddress();
                this.addressValue  = addressToInt(inetAddress.getAddress());
                if (arr.length <= 1) {
                    mask = 32;
                } else {
                    try {
                        mask = Integer.parseInt(arr[1].trim());
                    } catch (Exception e) {
                        logger.error("parse mask error. ip={} {}", ip, e.getMessage(), e);
                    }
                }
            } catch (Exception e) {
                logger.error("parse ip error. {}", e.getMessage(), e);
            }
        }

        public boolean match(InetAddress srcAddress) {
            int srcValue = addressToInt(srcAddress.getAddress());
            int srcMask = srcValue >>> (32 - mask);
            int localMask = addressValue >>> (32 - mask);
            return srcMask == localMask;
        }
    }


    public static Map<String, String> getUrlParamMap(String url){
        Map<String, String> paramMap = new HashMap<>();
        try {
            URL urlParse = new URL(url);
            String queryData = urlParse.getQuery();
            if(StringUtils.isEmpty(queryData)){
                return paramMap;
            }

            // 解码查询字符串
            String decodedQuery = URLDecoder.decode(queryData, "UTF-8");

            // 获取参数
            Pattern pattern = Pattern.compile("([^&=]+)=([^&=]*)");
            Matcher matcher = pattern.matcher(decodedQuery);
            while (matcher.find()) {
                paramMap.put(matcher.group(1), matcher.group(2));
            }
        }catch (Exception e){
            logger.error("getUrlParamMap error url:{}, e:{}", url, e.getMessage(), e);
        }
        return paramMap;
    }
}
