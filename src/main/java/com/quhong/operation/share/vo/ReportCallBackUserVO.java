package com.quhong.operation.share.vo;


import com.quhong.mysql.data.StaffBindUserRechargeJoinData;

import java.util.List;

public class ReportCallBackUserVO {
    private String addSubmitId; // 提交的id，验证的时候生成
    private List<ReasonVO> reasonList;
    private List<UserVO> userList;
    private List<StaffBindUserRechargeJoinData> staffBindUserList;

    public String getAddSubmitId() {
        return addSubmitId;
    }

    public void setAddSubmitId(String addSubmitId) {
        this.addSubmitId = addSubmitId;
    }

    public List<ReasonVO> getReasonList() {
        return reasonList;
    }

    public void setReasonList(List<ReasonVO> reasonList) {
        this.reasonList = reasonList;
    }

    public List<UserVO> getUserList() {
        return userList;
    }

    public void setUserList(List<UserVO> userList) {
        this.userList = userList;
    }

    public List<StaffBindUserRechargeJoinData> getStaffBindUserList() {
        return staffBindUserList;
    }

    public void setStaffBindUserList(List<StaffBindUserRechargeJoinData> staffBindUserList) {
        this.staffBindUserList = staffBindUserList;
    }

    public static class UserVO {
        private String name;
        private String head;
        private String rid;
        private String uid;
        private Integer regTime;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public String getRid() {
            return rid;
        }

        public void setRid(String rid) {
            this.rid = rid;
        }

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public Integer getRegTime() {
            return regTime;
        }

        public void setRegTime(Integer regTime) {
            this.regTime = regTime;
        }
    }

    public static class ReasonVO {
        private String rid; // 靓号展示靓号
        private int status;
        /**
         * 1,2,3,7,8
         * * 召回
         * * 1 召回-近2个月设备（本账号或者设备其他账号）活跃累计超过180分钟
         * * 2 召回-近2个月设备有其他账号充值
         * * 3 召回-注册时间小于2个月
         * * <p>
         * * 拓新
         * * 7 拓新-注册时间超过30天
         * * 8 拓新-同设备有其他账号
         *   9 拓新-是广告投放的用户
         */
        private String invalidReason;
        private String tnId;
        private String uid;
        private String medium; // 推广渠道  拓新有值

        public String getRid() {
            return rid;
        }

        public void setRid(String rid) {
            this.rid = rid;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getInvalidReason() {
            return invalidReason;
        }

        public void setInvalidReason(String invalidReason) {
            this.invalidReason = invalidReason;
        }

        public String getTnId() {
            return tnId;
        }

        public void setTnId(String tnId) {
            this.tnId = tnId;
        }

        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }

        public String getMedium() {
            return medium;
        }

        public void setMedium(String medium) {
            this.medium = medium;
        }
    }
}
