package com.quhong.operation.share.vo;

import com.alibaba.excel.annotation.ExcelProperty;


public class FriendshipVO {

    @ExcelProperty("日期")
    private String date;
    @ExcelProperty("私聊人数")
    private int chat; // 私聊人数
    @ExcelProperty("解锁置顶人数")
    private int unlockSticky; // 解锁置顶人数
    @ExcelProperty("解锁背景人数")
    private int unlockBg; // 解锁背景人数
    @ExcelProperty("解锁自定义背景人数")
    private int unlockCustomBg; // 解锁自定义背景人数
    @ExcelProperty("有置顶权的人数")
    private int haveSticky; // 有置顶权的人数
    @ExcelProperty("有背景设置权人数")
    private int haveBg; // 有背景设置权人数
    @ExcelProperty("有自定义背景设置权人数")
    private int haveCustomBg; // 有自定义背景设置权人数
    @ExcelProperty("结成好友人数")
    private int becomeFriends; // 结成好友人数
    @ExcelProperty("<1度人数")
    private int ltOne; // 亲密度<1度人数
    @ExcelProperty("1-2度人数")
    private int one; // 亲密度1-2度人数
    @ExcelProperty("2-3度人数")
    private int two; // 2-3度人数
    @ExcelProperty("3-4度人数")
    private int there; // 3-4度人数
    @ExcelProperty("4-5度人数")
    private int four; // 4-5度人数
    @ExcelProperty("5-10度人数")
    private int five; // 5-10度人数
    @ExcelProperty("10-20度人数")
    private int ten; // 10-20度人数
    @ExcelProperty("20-50度人数")
    private int twenty; // 20-50度人数
    @ExcelProperty("50度以上人数")
    private int gteFifty; // 50度以上人数


    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public int getChat() {
        return chat;
    }

    public void setChat(int chat) {
        this.chat = chat;
    }

    public int getUnlockSticky() {
        return unlockSticky;
    }

    public void setUnlockSticky(int unlockSticky) {
        this.unlockSticky = unlockSticky;
    }

    public int getUnlockBg() {
        return unlockBg;
    }

    public void setUnlockBg(int unlockBg) {
        this.unlockBg = unlockBg;
    }

    public int getUnlockCustomBg() {
        return unlockCustomBg;
    }

    public void setUnlockCustomBg(int unlockCustomBg) {
        this.unlockCustomBg = unlockCustomBg;
    }

    public int getHaveSticky() {
        return haveSticky;
    }

    public void setHaveSticky(int haveSticky) {
        this.haveSticky = haveSticky;
    }

    public int getHaveBg() {
        return haveBg;
    }

    public void setHaveBg(int haveBg) {
        this.haveBg = haveBg;
    }

    public int getHaveCustomBg() {
        return haveCustomBg;
    }

    public void setHaveCustomBg(int haveCustomBg) {
        this.haveCustomBg = haveCustomBg;
    }

    public int getBecomeFriends() {
        return becomeFriends;
    }

    public void setBecomeFriends(int becomeFriends) {
        this.becomeFriends = becomeFriends;
    }

    public int getLtOne() {
        return ltOne;
    }

    public void setLtOne(int ltOne) {
        this.ltOne = ltOne;
    }

    public int getOne() {
        return one;
    }

    public void setOne(int one) {
        this.one = one;
    }

    public int getTwo() {
        return two;
    }

    public void setTwo(int two) {
        this.two = two;
    }

    public int getThere() {
        return there;
    }

    public void setThere(int there) {
        this.there = there;
    }

    public int getFour() {
        return four;
    }

    public void setFour(int four) {
        this.four = four;
    }

    public int getFive() {
        return five;
    }

    public void setFive(int five) {
        this.five = five;
    }

    public int getTen() {
        return ten;
    }

    public void setTen(int ten) {
        this.ten = ten;
    }

    public int getTwenty() {
        return twenty;
    }

    public void setTwenty(int twenty) {
        this.twenty = twenty;
    }

    public int getGteFifty() {
        return gteFifty;
    }

    public void setGteFifty(int gteFifty) {
        this.gteFifty = gteFifty;
    }
}
