package com.quhong.operation.share.vo;

public class ChannelPromotionVO {

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 渠道来源id
     */
    private Integer sourceId;

    /**
     * 渠道来源名称
     */
    private String sourceName;

    /**
     * 渠道短链接
     */
    private String shortUrl;

    /**
     * 创建日期
     */
    private String date;

    /**
     * 负责人
     */
    private String opUser;

    /**
     * 状态
     */
    private String status;

    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getShortUrl() {
        return shortUrl;
    }

    public void setShortUrl(String shortUrl) {
        this.shortUrl = shortUrl;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getOpUser() {
        return opUser;
    }

    public void setOpUser(String opUser) {
        this.opUser = opUser;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "ChannelPromotionVO{" +
                "channelId=" + channelId +
                ", channelName='" + channelName + '\'' +
                ", sourceId=" + sourceId +
                ", sourceName='" + sourceName + '\'' +
                ", shortUrl='" + shortUrl + '\'' +
                ", date='" + date + '\'' +
                ", opUser='" + opUser + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
