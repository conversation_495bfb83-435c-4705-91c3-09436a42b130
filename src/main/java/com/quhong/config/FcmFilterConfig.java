package com.quhong.config;

import com.quhong.filter.AbstractFilterConfig;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Configuration
public class FcmFilterConfig extends AbstractFilterConfig {

    protected List<String> getExcludePaths() {
        return Arrays.asList(baseUrl + "fcmMessagePush");
    }
}
