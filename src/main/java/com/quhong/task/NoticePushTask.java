package com.quhong.task;

import com.quhong.config.AsyncConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.OfficialDeleteData;
import com.quhong.enums.SLangType;
import com.quhong.mongo.dao.NoticeNewDao;
import com.quhong.mongo.dao.OfficialDao;
import com.quhong.mongo.data.MongoActorData;
import com.quhong.mongo.data.OfficialData;
import com.quhong.mongo.data.SignTableData;
import com.quhong.monitor.MonitorSender;
import com.quhong.mysql.dao.SlaveMoneyDetailDao;
import com.quhong.service.FCMPushService;
import com.quhong.service.NoticePushService;
import com.quhong.utils.CollectionUtil;
import com.quhong.utils.K8sUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/15
 */
@Component
public class NoticePushTask {

    public final Logger logger = LoggerFactory.getLogger(getClass());


    @Resource
    private K8sUtils k8sUtils;
    @Resource
    private SlaveMoneyDetailDao slaveMoneyDetailDao;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private NoticePushService noticePushService;
    @Resource
    private OfficialDao officialDao;
    @Resource
    private NoticeNewDao noticeNewDao;
    @Resource
    private FCMPushService fcmPushService;

    @PostConstruct
    public void init() {
        // sud游戏
    }

    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0 0/2 * * * ?")
    public void noticePushTask() {
        if (k8sUtils.isMasterFromCache()) {
            noticePushService.officialMsgPush();
        }
    }

    /**
     * 删除15天前的官方推送，每天北京时间10点执行
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0 0 2 * * ?")
    public void deleteOfficialPush() {
        logger.info("deleteOfficialPush");
        if (k8sUtils.isMasterFromCache()) {
//            onClearOfficialPushTick();
            onClearOfficialPushTick2();
        }
    }

    private void onClearOfficialPushTick() {
        long millis = System.currentTimeMillis();
        int endTime = DateHelper.getNowSeconds() - 15 * 86400;
        List<OfficialDeleteData> officialDataList = officialDao.getLastDayOfficialDataList(endTime, 10000);
        logger.info("onClearOfficialPushTick start. officialDataList={}", officialDataList.size());
        if (CollectionUtils.isEmpty(officialDataList)) {
            return;
        }
        List<String> objIdList = officialDataList.stream().map(item -> item.get_id().toString()).collect(Collectors.toList());
        officialDao.clearOfficialMsg(objIdList);
        noticeNewDao.clearNoticeNewMsg(objIdList);
        logger.info("onClearOfficialPushTick online. timeMillis={}", System.currentTimeMillis() - millis);
    }

    private void onClearOfficialPushTick2() {
        long millis = System.currentTimeMillis();
        int endTime = DateHelper.getNowSeconds() - 15 * 86400;

        // 分批处理，避免一次性加载大量数据
        final int BATCH_SIZE = 2000; // 每批处理2000条
        int processedCount = 0;
        int totalProcessed = 0;

        logger.info("onClearOfficialPushTick start.");
        int count = 0;
        do {
            // 分批获取数据
            List<OfficialDeleteData> officialDataList = officialDao.getLastDayOfficialDataList(endTime, BATCH_SIZE);
            if (CollectionUtils.isEmpty(officialDataList)) {
                break;
            }
            processedCount = officialDataList.size();
            totalProcessed += processedCount;

            logger.info("Processing batch: {} records", processedCount);

            // 使用Stream避免创建中间List，减少内存占用
            try {
                if (count >= 10000) {
                    logger.info("break onClearOfficialPushTick count={}", count);
                    break;
                }
                List<String> objIdList = officialDataList.parallelStream()
                        .map(item -> item.get_id().toString())
                        .collect(Collectors.toList());
                // 批量删除
                officialDao.clearOfficialMsg(objIdList);
                noticeNewDao.clearNoticeNewMsg(objIdList);
                // 显式清理引用，帮助GC
                objIdList.clear();
                officialDataList.clear();
                count++;
            } catch (Exception e) {
                logger.error("Error processing batch, endTime={}, batchSize={}, error={}",
                        endTime, processedCount, e.getMessage(), e);
                count++;
                // 继续处理下一批，不因单批失败而中断整个流程
            }

        } while (processedCount == BATCH_SIZE); // 如果返回的数据少于BATCH_SIZE，说明已经处理完了

        logger.info("onClearOfficialPushTick completed. totalProcessed={}, timeMillis={}",
                totalProcessed, System.currentTimeMillis() - millis);
    }



    /**
     * FCM每日签到消息通知，UTC+3 20:00
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0 0 17 * * ?")
    public void dailySignNotice() {
        if (!k8sUtils.isMaster()) {
            return;
        }
        fcmPushService.dailySignFcmPush();
    }


    /**
     * 连续三天不活跃的用户推送
     * 每小时执行一次
     * <a href="https://www.tapd.cn/tapd_fe/20792731/story/detail/1120792731001019314">...</a>
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0 0 0/1 * * ?")
    public void userNoActive() {
        DateHelper.ARABIAN.formatDateInHour();

        if (k8sUtils.isMasterFromCache()) {

            // synchronized (weeklyStarDeliver) {
            //     weeklyStarDeliver.deliver();
            // }
        }
    }

}
