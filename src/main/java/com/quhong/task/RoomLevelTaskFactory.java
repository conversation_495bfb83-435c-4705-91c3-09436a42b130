package com.quhong.task;

import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.pools.TaskPool;
import com.quhong.core.concurrency.tasks.ITask;
import org.springframework.stereotype.Component;

@Component
public class RoomLevelTaskFactory extends BaseTaskFactory {
    public static RoomLevelTaskFactory getFactory(){
        return (RoomLevelTaskFactory) factory;
    }

    /**
     * 慢业务任务池
     */
    private TaskPool msgPool;

    @Override
    public void init() {
        super.init();
        msgPool = new TaskPool().init(8,"user");
    }

    public void addPush(ITask task){
        msgPool.add(task);
    }
}
