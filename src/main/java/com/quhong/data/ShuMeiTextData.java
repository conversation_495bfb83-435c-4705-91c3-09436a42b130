package com.quhong.data;


import com.alibaba.fastjson.JSON;

import java.util.List;

/**
 * https://help.ishumei.com/docs/tj/text/newest/developDoc/
 */
public class ShuMeiTextData {
    private int code;
    private String message;
    private String requestId;
    private String riskDescription;
    private String riskLabel1;
    private String riskLabel2;
    private String riskLabel3;
    private String riskLevel; // 对文本的处理建议，可能包含的值包括[PASS/REVIEW/REJECT]
    private RiskDetail riskDetail;
    private String businessLabels;
    private String auxInfo;
    private List<Label> allLabels;
    private Integer finalResult; // 是否最终结果
    private Integer resultType; // 	0:机审，1:人审

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getRiskDescription() {
        return riskDescription;
    }

    public void setRiskDescription(String riskDescription) {
        this.riskDescription = riskDescription;
    }

    public String getRiskLabel1() {
        return riskLabel1;
    }

    public void setRiskLabel1(String riskLabel1) {
        this.riskLabel1 = riskLabel1;
    }

    public String getRiskLabel2() {
        return riskLabel2;
    }

    public void setRiskLabel2(String riskLabel2) {
        this.riskLabel2 = riskLabel2;
    }

    public String getRiskLabel3() {
        return riskLabel3;
    }

    public void setRiskLabel3(String riskLabel3) {
        this.riskLabel3 = riskLabel3;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }

    public RiskDetail getRiskDetail() {
        return riskDetail;
    }

    public void setRiskDetail(RiskDetail riskDetail) {
        this.riskDetail = riskDetail;
    }

    public String getBusinessLabels() {
        return businessLabels;
    }

    public void setBusinessLabels(String businessLabels) {
        this.businessLabels = businessLabels;
    }

    public String getAuxInfo() {
        return auxInfo;
    }

    public void setAuxInfo(String auxInfo) {
        this.auxInfo = auxInfo;
    }

    public List<Label> getAllLabels() {
        return allLabels;
    }

    public void setAllLabels(List<Label> allLabels) {
        this.allLabels = allLabels;
    }

    public Integer getFinalResult() {
        return finalResult;
    }

    public void setFinalResult(Integer finalResult) {
        this.finalResult = finalResult;
    }

    public Integer getResultType() {
        return resultType;
    }

    public void setResultType(Integer resultType) {
        this.resultType = resultType;
    }

    public static class Label {
        private double probability; // 文本包含违规信息的概率，取值为0~1
        private String riskDescription;
        private RiskDetail riskDetail;

        private String riskLabel1;
        private String riskLabel2;
        private String riskLabel3;
        private String riskLevel; // 对文本的处理建议，可能包含的值包括[PASS/REVIEW/REJECT]

        public double getProbability() {
            return probability;
        }

        public void setProbability(double probability) {
            this.probability = probability;
        }

        public String getRiskDescription() {
            return riskDescription;
        }

        public void setRiskDescription(String riskDescription) {
            this.riskDescription = riskDescription;
        }

        public RiskDetail getRiskDetail() {
            return riskDetail;
        }

        public void setRiskDetail(RiskDetail riskDetail) {
            this.riskDetail = riskDetail;
        }

        public String getRiskLabel1() {
            return riskLabel1;
        }

        public void setRiskLabel1(String riskLabel1) {
            this.riskLabel1 = riskLabel1;
        }

        public String getRiskLabel2() {
            return riskLabel2;
        }

        public void setRiskLabel2(String riskLabel2) {
            this.riskLabel2 = riskLabel2;
        }

        public String getRiskLabel3() {
            return riskLabel3;
        }

        public void setRiskLabel3(String riskLabel3) {
            this.riskLabel3 = riskLabel3;
        }

        public String getRiskLevel() {
            return riskLevel;
        }

        public void setRiskLevel(String riskLevel) {
            this.riskLevel = riskLevel;
        }

        @Override
        public String toString() {
            return JSON.toJSONString(this);
        }
    }

    public static class RiskDetail {
        private List<RiskDetailMatched> matchedLists;
        private List<RiskDetailSegments> riskSegments;

        public List<RiskDetailMatched> getMatchedLists() {
            return matchedLists;
        }

        public void setMatchedLists(List<RiskDetailMatched> matchedLists) {
            this.matchedLists = matchedLists;
        }

        public List<RiskDetailSegments> getRiskSegments() {
            return riskSegments;
        }

        public void setRiskSegments(List<RiskDetailSegments> riskSegments) {
            this.riskSegments = riskSegments;
        }

        public static class RiskDetailMatched {
            private String name; // 命中的名单名称
            private List<RiskDetailMatchedWords> words;

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public List<RiskDetailMatchedWords> getWords() {
                return words;
            }

            public void setWords(List<RiskDetailMatchedWords> words) {
                this.words = words;
            }

            public static class RiskDetailMatchedWords {
                private String word; //命中的敏感词
                private List<Integer> position;

                public String getWord() {
                    return word;
                }

                public void setWord(String word) {
                    this.word = word;
                }

                public List<Integer> getPosition() {
                    return position;
                }

                public void setPosition(List<Integer> position) {
                    this.position = position;
                }

                @Override
                public String toString() {
                    return JSON.toJSONString(this);
                }
            }

            @Override
            public String toString() {
                return JSON.toJSONString(this);
            }
        }

        public static class RiskDetailSegments {
            private String segment; // 高风险内容片段
            private List<Integer> position;

            public String getSegment() {
                return segment;
            }

            public void setSegment(String segment) {
                this.segment = segment;
            }

            public List<Integer> getPosition() {
                return position;
            }

            public void setPosition(List<Integer> position) {
                this.position = position;
            }

            @Override
            public String toString() {
                return JSON.toJSONString(this);
            }
        }

        @Override
        public String toString() {
            return JSON.toJSONString(this);
        }
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
