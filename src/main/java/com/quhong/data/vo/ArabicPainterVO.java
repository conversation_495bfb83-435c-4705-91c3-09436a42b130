package com.quhong.data.vo;


import java.util.List;
import java.util.Map;
import java.util.Set;

public class ArabicPainterVO extends OtherRankConfigVO {
    private List<ArabicPainterMomentInfoVO> myMomentListVO; // 我的创作列表,按天分割
    private List<ArabicPainterDayVO> arabicPainterDayVOList; // 推荐的创作列表,按天分割
    private int enterType; // 1 首次进入H5 2 创作后 3 未创作时 4 活动结束后
    private String recommendText; // 当日推荐文案
    private int recommendTextState; // 当日推荐文案点赞状态 0未点赞 1 点赞
    private List<String> recommendCommentList; // 推荐评论文案列表
    private List<ArabicPainterMomentInfoVO> rankMomentListVO; // 作品点赞排行榜
    private ArabicPainterMomentInfoVO myRankVO; // 我的排名
    private Integer topicRid;  // 话题rid
    private Integer commentCount; // 评论次数

    public static class ArabicPainterInfo {
        private int firstEntryTime; // 第一次进入活动页时间
        private int firstWriteTime; // 第一次创作时间
        private Map<String, String> dayMapMomentId; // key日期 "2025-03-10" 值为已创作作品的momentId
        private Map<String, Integer> dayMapLikeState; // key日期 "2025-03-10" 值为是否点赞了当天的文案  0未点 1已点
        private Map<String, Set<String>> dayMapCommentAidList; // key日期 "2025-03-10" 值为是被评论对象的aid列表

        public int getFirstEntryTime() {
            return firstEntryTime;
        }

        public void setFirstEntryTime(int firstEntryTime) {
            this.firstEntryTime = firstEntryTime;
        }

        public int getFirstWriteTime() {
            return firstWriteTime;
        }

        public void setFirstWriteTime(int firstWriteTime) {
            this.firstWriteTime = firstWriteTime;
        }

        public Map<String, String> getDayMapMomentId() {
            return dayMapMomentId;
        }

        public void setDayMapMomentId(Map<String, String> dayMapMomentId) {
            this.dayMapMomentId = dayMapMomentId;
        }

        public Map<String, Integer> getDayMapLikeState() {
            return dayMapLikeState;
        }

        public void setDayMapLikeState(Map<String, Integer> dayMapLikeState) {
            this.dayMapLikeState = dayMapLikeState;
        }

        public Map<String, Set<String>> getDayMapCommentAidList() {
            return dayMapCommentAidList;
        }

        public void setDayMapCommentAidList(Map<String, Set<String>> dayMapCommentAidList) {
            this.dayMapCommentAidList = dayMapCommentAidList;
        }
    }

    public static class ArabicPainterDayVO {
        private String dayStr;
        private List<ArabicPainterMomentInfoVO> recommendMomentListVOList;

        public String getDayStr() {
            return dayStr;
        }

        public void setDayStr(String dayStr) {
            this.dayStr = dayStr;
        }

        public List<ArabicPainterMomentInfoVO> getRecommendMomentListVOList() {
            return recommendMomentListVOList;
        }

        public void setRecommendMomentListVOList(List<ArabicPainterMomentInfoVO> recommendMomentListVOList) {
            this.recommendMomentListVOList = recommendMomentListVOList;
        }
    }

    public static class ArabicPainterMomentInfoVO extends PainterHallVO.MomentInfo {
        private String dayStr;
        private Integer rank; // -1为50+,其他为当前排名
        private Integer uploadCount; // 上传次数

        private String head; // 用户头像
        private String aid; // 创作用户的uid


        public String getDayStr() {
            return dayStr;
        }

        public void setDayStr(String dayStr) {
            this.dayStr = dayStr;
        }

        public Integer getRank() {
            return rank;
        }

        public void setRank(Integer rank) {
            this.rank = rank;
        }

        public Integer getUploadCount() {
            return uploadCount;
        }

        public void setUploadCount(Integer uploadCount) {
            this.uploadCount = uploadCount;
        }

        public String getHead() {
            return head;
        }

        public void setHead(String head) {
            this.head = head;
        }

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }
    }

    public List<ArabicPainterMomentInfoVO> getMyMomentListVO() {
        return myMomentListVO;
    }

    public void setMyMomentListVO(List<ArabicPainterMomentInfoVO> myMomentListVO) {
        this.myMomentListVO = myMomentListVO;
    }


    public int getEnterType() {
        return enterType;
    }

    public void setEnterType(int enterType) {
        this.enterType = enterType;
    }

    public String getRecommendText() {
        return recommendText;
    }

    public void setRecommendText(String recommendText) {
        this.recommendText = recommendText;
    }

    public int getRecommendTextState() {
        return recommendTextState;
    }

    public void setRecommendTextState(int recommendTextState) {
        this.recommendTextState = recommendTextState;
    }

    public ArabicPainterMomentInfoVO getMyRankVO() {
        return myRankVO;
    }

    public void setMyRankVO(ArabicPainterMomentInfoVO myRankVO) {
        this.myRankVO = myRankVO;
    }

    public List<ArabicPainterMomentInfoVO> getRankMomentListVO() {
        return rankMomentListVO;
    }

    public void setRankMomentListVO(List<ArabicPainterMomentInfoVO> rankMomentListVO) {
        this.rankMomentListVO = rankMomentListVO;
    }

    public List<String> getRecommendCommentList() {
        return recommendCommentList;
    }

    public void setRecommendCommentList(List<String> recommendCommentList) {
        this.recommendCommentList = recommendCommentList;
    }

    public Integer getTopicRid() {
        return topicRid;
    }

    public void setTopicRid(Integer topicRid) {
        this.topicRid = topicRid;
    }

    public Integer getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(Integer commentCount) {
        this.commentCount = commentCount;
    }

    public List<ArabicPainterDayVO> getArabicPainterDayVOList() {
        return arabicPainterDayVOList;
    }

    public void setArabicPainterDayVOList(List<ArabicPainterDayVO> arabicPainterDayVOList) {
        this.arabicPainterDayVOList = arabicPainterDayVOList;
    }
}
