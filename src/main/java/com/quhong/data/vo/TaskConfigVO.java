package com.quhong.data.vo;

public class TaskConfigVO {
    private Integer totalProcess;    // 总任务进度
    private Integer currentProcess;      // 当前进度
    private String taskKey;
    private String taskIcon;
    private String nameEn;
    private String nameAr;
    private Integer status;
    private String targetId;
    private String targetIcon;
    private String giftIcon;
    private String resourceKey;

    public TaskConfigVO() {
    }

    public TaskConfigVO(Integer totalProcess, Integer currentProcess, String taskKey, String taskIcon, String nameEn, String nameAr, Integer status, String targetId, String targetIcon, String giftIcon, String resourceKey) {
        this.totalProcess = totalProcess;
        this.currentProcess = currentProcess;
        this.taskKey = taskKey;
        this.taskIcon = taskIcon;
        this.nameEn = nameEn;
        this.nameAr = nameAr;
        this.status = status;
        this.targetId = targetId;
        this.targetIcon = targetIcon;
        this.giftIcon = giftIcon;
        this.resourceKey = resourceKey;
    }

    public Integer getTotalProcess() {
        return totalProcess;
    }

    public void setTotalProcess(Integer totalProcess) {
        this.totalProcess = totalProcess;
    }

    public Integer getCurrentProcess() {
        return currentProcess;
    }

    public void setCurrentProcess(Integer currentProcess) {
        this.currentProcess = currentProcess;
    }

    public String getTaskKey() {
        return taskKey;
    }

    public void setTaskKey(String taskKey) {
        this.taskKey = taskKey;
    }

    public String getTaskIcon() {
        return taskIcon;
    }

    public void setTaskIcon(String taskIcon) {
        this.taskIcon = taskIcon;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getNameAr() {
        return nameAr;
    }

    public void setNameAr(String nameAr) {
        this.nameAr = nameAr;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getTargetId() {
        return targetId;
    }

    public void setTargetId(String targetId) {
        this.targetId = targetId;
    }

    public String getTargetIcon() {
        return targetIcon;
    }

    public void setTargetIcon(String targetIcon) {
        this.targetIcon = targetIcon;
    }

    public String getGiftIcon() {
        return giftIcon;
    }

    public void setGiftIcon(String giftIcon) {
        this.giftIcon = giftIcon;
    }

    public String getResourceKey() {
        return resourceKey;
    }

    public void setResourceKey(String resourceKey) {
        this.resourceKey = resourceKey;
    }
}
