package com.quhong.data.vo;

import com.quhong.mongo.data.MomentData;

import java.util.List;

public class CommentVO {
    private String comment_id;
    private String comment;
    private String uid;
    private int c_time;
    private String head;
    private String name;
    private int gender;
    private int vipLevel;
    private String reply_to_uid;
    private String reply_to_name = "";
    private List<MomentData.AtUser> at_list;
    private List<CommentVO> subList; // 折叠后的回复列表(仅有最近的一条记录，commentList接口且replyCount=1时才有值)
    private int replyCount; // 回复数量
    private int likeCount; // 点赞数量
    private int isLike; // 是否已经点赞
    private int del; // 是否显示删除按钮，管理员和博主=1
    private int firstComment;  // 是否首评
    private int hotComment;    // 是否热评

    public String getComment_id() {
        return comment_id;
    }

    public void setComment_id(String comment_id) {
        this.comment_id = comment_id;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getC_time() {
        return c_time;
    }

    public void setC_time(int c_time) {
        this.c_time = c_time;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public int getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(int likeCount) {
        this.likeCount = likeCount;
    }

    public String getReply_to_uid() {
        return reply_to_uid;
    }

    public void setReply_to_uid(String reply_to_uid) {
        this.reply_to_uid = reply_to_uid;
    }

    public String getReply_to_name() {
        return reply_to_name;
    }

    public void setReply_to_name(String reply_to_name) {
        this.reply_to_name = reply_to_name;
    }

    public List<MomentData.AtUser> getAt_list() {
        return at_list;
    }

    public void setAt_list(List<MomentData.AtUser> at_list) {
        this.at_list = at_list;
    }

    public List<CommentVO> getSubList() {
        return subList;
    }

    public void setSubList(List<CommentVO> subList) {
        this.subList = subList;
    }

    public int getDel() {
        return del;
    }

    public void setDel(int del) {
        this.del = del;
    }

    public int getFirstComment() {
        return firstComment;
    }

    public void setFirstComment(int firstComment) {
        this.firstComment = firstComment;
    }

    public int getReplyCount() {
        return replyCount;
    }

    public void setReplyCount(int replyCount) {
        this.replyCount = replyCount;
    }

    public int getIsLike() {
        return isLike;
    }

    public void setIsLike(int isLike) {
        this.isLike = isLike;
    }

    public int getHotComment() {
        return hotComment;
    }

    public void setHotComment(int hotComment) {
        this.hotComment = hotComment;
    }
}
