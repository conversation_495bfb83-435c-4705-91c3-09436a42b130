package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <AUTHOR>
 * @date 2023/8/3
 */
@TableName("t_room_like")
public class RoomLikeData {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 房间id
     */
    private String roomId;

    /**
     * 用户uid
     */
    private String uid;

    /**
     * 点赞数
     */
    private Integer likesNum;

    /**
     * 点赞日期
     */
    private String likeDate;

    /**
     * 创建时间
     */
    private Integer ctime;

    public RoomLikeData() {
    }

    public RoomLikeData(String roomId, String uid, Integer likesNum, String likeDate, Integer ctime) {
        this.roomId = roomId;
        this.uid = uid;
        this.likesNum = likesNum;
        this.likeDate = likeDate;
        this.ctime = ctime;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getLikesNum() {
        return likesNum;
    }

    public void setLikesNum(Integer likesNum) {
        this.likesNum = likesNum;
    }

    public String getLikeDate() {
        return likeDate;
    }

    public void setLikeDate(String likeDate) {
        this.likeDate = likeDate;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
