package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.RoomSetupEvent;
import com.quhong.config.RoomLevelConfig;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.constant.MatchConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.data.*;
import com.quhong.data.dto.*;
import com.quhong.data.vo.*;
import com.quhong.dto.ImageDTO;
import com.quhong.dto.TextDTO;
import com.quhong.enums.*;
import com.quhong.exception.CommonException;
import com.quhong.feign.IDetectService;
import com.quhong.feign.IGameService;
import com.quhong.handler.HttpEnvData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.monitor.MonitorSender;
import com.quhong.msg.obj.UserInfoObject;
import com.quhong.msg.room.*;
import com.quhong.mysql.dao.*;
import com.quhong.mysql.data.*;
import com.quhong.redis.*;
import com.quhong.room.RoomTags;
import com.quhong.room.RoomWebSender;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.room.redis.MicApplyRedis;
import com.quhong.room.redis.MicFrameRedis;
import com.quhong.sdk.agora.AgoraService;
import com.quhong.sdk.zego.ZegoService;
import com.quhong.utils.MatchUtils;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.PageVO;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
public class RoomSettingsService {

    private static final Logger logger = LoggerFactory.getLogger(RoomSettingsService.class);

    private static final int CANCEL_PWD = 0;
    private static final int SET_PWD = 1;

    private static final String USE_ROOM_LOCK_LIMIT_V1_TIP = "Only Sultan or above can use room lock for free.";
    private static final String USE_ROOM_LOCK_LIMIT_V1_TIP_AR = "فقط السلطان أو ما فوق يمكنه استخدام قفل الغرفة مجانا";
    private static final String USE_ROOM_LOCK_LIMIT_V2_TIP = "Only Tycoon 3 or above can use room lock for free.";
    private static final String USE_ROOM_LOCK_LIMIT_V2_TIP_AR = "فقط تميز 3 أو ما فوق يمكنه استخدام قفل الغرفة مجانا";
    private static final String USE_ROOM_LOCK_LIMIT_TIP = "Only Tycoon 3 or above can use room lock for free.";
    private static final String USE_ROOM_LOCK_LIMIT_TIP_AR = "فقط ال VIP3 أو ما فوق يمكنه استخدام قفل الغرفة مجانا";

    private static final int DEFAULT_BACKGROUND_ID = 233;
    private static final MongoThemeData DEFAULT_BACKGROUND = new MongoThemeData();

    private static final String NOTICE_BODY = "Sorry,your avatar didn't pass the review, please upload your new avatar.";
    private static final String NOTICE_BODY_AR = "المعتذر،لم تنجح صورتك الرمزية في المراجعة، يرجى تحميل الصورة الرمزية الجديد";

    private static final String KICK_OUT_ROOM = "Kicked out of the room by %s";
    private static final String KICK_OUT_ROOM_AR = "طرد من الغرفة بواسطة %s";
    private static final String ADD_ROOM_BLACKLIST = "Added to the room blacklist by %s";
    private static final String ADD_ROOM_BLACKLIST_AR = "تم الحظر بواسطة %s";
    private static final String DEL_ROOM_BLACKLIST = "Removed from room blacklist by %s";
    private static final String DEL_ROOM_BLACKLIST_AR = "تم إلغاء الحظر بواسطة %s";
    private static final String KICK_OUT_MIC = "Moved down the Mic by %s";
    private static final String KICK_OUT_MIC_AR = "تم النقل إلى أسفل الميكروفون بمقدار %s";
    private static final String MUTE = "Banned by %s";
    private static final String MUTE_AR = "حظر بواسطة %s";
    private static final String UNMUTE = "Unbanned by %s";
    private static final String UNMUTE_AR = "تم إلغاء الحظر بواسطة %s";

    @Resource
    private MongoRoomDao roomDao;
    @Resource
    private RoomMemberDao roomMemberDao;
    @Resource
    private RoomTags roomTags;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private ZegoService zegoService;
    @Resource
    private AgoraService agoraService;
    @Resource
    private IDetectService detectService;
    @Resource
    private RoomMemberDao memberDao;
    @Resource
    private RoomLockDao roomLockDao;
    @Resource
    private RoomPwdRedis roomPwdRedis;
    @Resource
    private RoomActionLogDao roomActionLogDao;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private MongoThemeDao themeDao;
    @Resource
    private BadgeDao badgeDao;
    @Resource
    private ConquerRedis conquerRedis;
    @Resource
    private BeautifulRidDao beautifulRidDao;
    @Resource
    private CommonConfig commonConfig;
    @Resource
    private BlockRedis blockRedis;
    @Resource
    private RoomActorCache roomActorCache;
    @Resource
    private RoomAdminRedis roomAdminRedis;
    @Resource
    private UserMonitorDao userMonitorDao;
    @Resource
    private UploadBackgroundDao uploadBackgroundDao;
    @Resource
    private MineBackgroundDao mineBackgroundDao;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private RoomThemeService roomThemeService;
    @Resource
    private ConquerActivityDao conquerActivityDao;
    @Resource
    private MicApplyRedis micApplyRedis;
    @Resource
    private OfficialDao officialDao;
    @Resource
    private NoticeNewDao noticeNewDao;
    @Resource
    private MonitorNsfwDao monitorNsfwDao;
    @Resource
    private BaseInitData baseInitData;
    @Resource
    private MicFrameRedis micFrameRedis;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private RoomLevelConfig roomLevelConfig;
    @Resource
    private FollowRoomDao followRoomDao;
    @Resource
    private RoomLevelService roomLevelService;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private RoomAllowanceLogDao roomAllowanceLogDao;
    @Resource
    private RoomMicThemeDao roomMicThemeDao;
    @Resource
    private TruthOrDareV2Redis truthOrDareV2Redis;
    @Resource
    private IGameService iGameService;

    @PostConstruct
    public void init() {
        MongoThemeData themeData = themeDao.findThemeData(DEFAULT_BACKGROUND_ID);
        if (null != themeData) {
            BeanUtils.copyProperties(themeData, DEFAULT_BACKGROUND);
        }
    }

    public RoomSettingsVO roomSetting(RoomSettingsDTO req) {
        RoomSettingsVO vo = new RoomSettingsVO();
        MongoRoomData mongoRoomData = roomDao.findData(RoomUtils.formatRoomId(req.getRoomId()));
        if (null == mongoRoomData) {
            logger.error("cannot find room roomId={} uid={}", req.getRoomId(), req.getUid());
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        BeanUtils.copyProperties(mongoRoomData, vo);
        vo.setTagList(SLangType.ENGLISH == req.getSlang() ? roomTags.getEnTags() : roomTags.getArTags());
        return vo;
    }

    public Object updateTag(RoomSettingsDTO req) {
        RoomRoleData roomRoleData = roomMemberDao.getRoleData(req.getRoomId(), req.getUid());
        if (!roomRoleData.isHostOrVice()) {
            logger.info("actor is not host or vice host. can not update tag. roomId={} uid={}", req.getRoomId(), req.getUid());
            throw new CommonException(RoomHttpCode.AUTH_ERROR);
        }
        roomDao.updateRoomTag(req.getRoomId(), req.getTagId());
        eventReport.track(new EventDTO(new RoomSetupEvent(req.getRoomId(), "room_tag", String.valueOf(req.getTagId()))));
        return null;
    }

    /**
     * 设置/修改/取消房间密码
     */
    public SetRoomPwdVO setRoomPwd(RoomPwdDTO req) {
        String uid = req.getUid();
        String roomId = req.getRoomId();
        int os = req.getOs();
        int versionCode = req.getVersioncode();
        int slang = req.getSlang();
        int opt = req.getOpt();
        MongoRoomData roomData = roomDao.findData(roomId);
        if (roomData == null) {
            logger.error("can not find room data. uid={} roomId={}", uid, roomId);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        // 锁房检测: 先关闭真心话大冒险主题游戏
        String gameId = truthOrDareV2Redis.getGameIdByRoomId(roomId);
        if (!ObjectUtils.isEmpty(gameId)){
            throw new CommonException(RoomHttpCode.CAN_NOT_SET_ROOM_LOCK);
        }

        RoomLockData roomLockData = roomLockDao.findData(uid);
        String msg = slang != 2 ? USE_ROOM_LOCK_LIMIT_TIP : USE_ROOM_LOCK_LIMIT_TIP_AR;
        if (!RoomNewService.STAFF_ROOM_SET.contains(uid)) {
            if (roomLockData == null) {
                roomData.setPwd("");
                roomDao.save(roomData);
                roomPwdRedis.remove(roomId);
                if (os != 1 && versionCode >= 153 && versionCode <= 155) {
                    msg = slang != 2 ? USE_ROOM_LOCK_LIMIT_V1_TIP : USE_ROOM_LOCK_LIMIT_V1_TIP_AR;
                } else if (os == 1 && versionCode >= 430 && versionCode <= 440) {
                    msg = slang != 2 ? USE_ROOM_LOCK_LIMIT_V1_TIP : USE_ROOM_LOCK_LIMIT_V1_TIP_AR;
                } else if (os != 1 && versionCode >= 156) {
                    msg = slang != 2 ? USE_ROOM_LOCK_LIMIT_V2_TIP : USE_ROOM_LOCK_LIMIT_V2_TIP_AR;
                } else if (os == 1 && versionCode >= 450) {
                    msg = slang != 2 ? USE_ROOM_LOCK_LIMIT_V2_TIP : USE_ROOM_LOCK_LIMIT_V2_TIP_AR;
                }
                logger.info("msg={}. uid={} roomId={}", msg, uid, roomId);
                throw new CommonException(new HttpCode(41, msg));
            }
        }
        if (opt == CANCEL_PWD) {
            RoomChangePwdPushMsg pushMsg = new RoomChangePwdPushMsg();
            pushMsg.setFromUid(RoomUtils.getRoomHostId(roomId));
            pushMsg.setRoomId(roomId);
            roomWebSender.sendRoomWebMsg(roomId, uid, pushMsg, true);
            roomPwdRedis.remove(roomId);
            if (whiteTestDao.isMemberByType(roomId, WhiteTestDao.WHITE_TYPE_ROOM_ID)) {
                monitorSender.info("ustar_java_exception", "测试房间取消密码", "房间id：" + roomData.getOwnerRid());
            }
            if (StringUtils.isEmpty(roomData.getPwd())) {
                return null;
            } else {
                roomData.setPwd("");
                roomDao.save(roomData);
                return buildSetRoomPwdVO(uid, roomId, null);
            }
        }
        if (opt == SET_PWD) {
            if (!NumberUtils.isDigits(req.getPwd())) {
                logger.error("set room pwd params error. uid={} roomId={}", uid, roomId);
                throw new CommonException(HttpCode.PARAM_ERROR);
            }
            // 发送im
            RoomChangePwdPushMsg pushMsg = new RoomChangePwdPushMsg();
            pushMsg.setFromUid(RoomUtils.getRoomHostId(roomId));
            pushMsg.setRoomId(roomId);
            roomWebSender.sendRoomWebMsg(roomId, uid, pushMsg, true);
            String newPwd = checkPwd(req.getPwd());
            if (newPwd.equals(roomData.getPwd())) {
                // 密码没变
                return null;
            } else {
                roomData.setPwd(newPwd);
                roomDao.save(roomData);
                roomPwdRedis.setRoomPwd(roomId, newPwd);
                return buildSetRoomPwdVO(uid, roomId, newPwd);
            }
        }
        return null;
    }

    private SetRoomPwdVO buildSetRoomPwdVO(String uid, String roomId, String newPwd) {
        String roomStreamId = genStreamId(roomId, newPwd);
        String agoraToken = agoraService.getAgoraToken(roomStreamId, uid);
        String zegoToken = zegoService.getZegoToken(uid, roomStreamId);
        SetRoomPwdVO vo = new SetRoomPwdVO();
        vo.setRoom_stream_id(roomStreamId);
        vo.setZego_token(zegoToken);
        vo.setAgora_token(agoraToken);
        return vo;
    }

    private String checkPwd(String pwd) {
        if (!NumberUtils.isDigits(pwd)) {
            return "";
        }
        return handleArbNum(pwd);
    }

    /**
     * 获取房间信息
     */
    public RoomInfoVO getRoomInfo(RoomDTO req) {
        String roomId = req.getRoomId();
        String uid = req.getUid();
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(roomId) || roomId.length() < 2) {
            logger.error("uid or roomId params error. uid={} roomId={}", uid, roomId);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        MongoRoomData data = roomDao.findData(roomId);
        if (data == null) {
            logger.error("can not find room data. uid={} roomId={}", uid, roomId);
            throw new CommonException(41, "can not find room data");
        }
        String ownerUid = RoomUtils.getRoomHostId(roomId);
        ActorData actorData = actorDao.getActorDataFromCache(ownerUid);
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", ownerUid);
            throw new CommonException(42, "can not find actor data");
        }
        RoomInfoVO vo = new RoomInfoVO();
        // 设置房主信息
        vo.setAid(actorData.getUid());
        vo.setRid(actorData.getRid());
        vo.setRidData(actorData.getRidData());
        vo.setName(actorData.getName());
        vo.setGender(actorData.getFb_gender());
        vo.setAge(actorData.getAge());
        vo.setVipLevel(vipInfoDao.getIntVipLevelFromCache(ownerUid));
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), vo.getVipLevel()));
        vo.setMicFrame(micFrameRedis.getMicSourceFromCache(ownerUid));
        vo.setIdentify(0);
        vo.setUserLevel(userLevelDao.getUserLevel(ownerUid));
        vo.setBadgeList(badgeDao.getBadgeList(ownerUid));
        vo.setIsBeautifulRid(beautifulRidDao.isBeautifulRid(ownerUid));
        // 设置房间信息
        vo.setTag(data.getTag());
        vo.setTagName(roomTags.getTagNameById(data.getTag(), req.getSlang()));
        vo.setAnnounce(data.getAnnounce());
        vo.setPrivi(data.getPrivi());
        vo.setFee(data.getFee());
        vo.setFeetype(data.getFeetype());
        vo.setComp(data.getComp());
        vo.setPwd(StringUtils.isEmpty(data.getPwd()) ? 0 : 1);
        vo.setRoomId(data.getRid());
        vo.setCountry(data.getCountry());
        vo.setOnline(data.getOnline());
        vo.setRoom_name(data.getName());
        vo.setRoom_head(ImageUrlGenerator.generateRoomUserUrl(data.getHead(), vo.getVipLevel()));
        vo.setRoom_type(data.getRoom_type());
        vo.setRoom_pk(data.getRoom_pk());
        vo.setChat_locked(data.getChat_locked() > 0 ? 1 : 0);
        vo.setPic_locked(data.getPic_locked());
        vo.setTextLimit(null == data.getTextLimit() ? -1 : data.getTextLimit());
        RoomMemberData memberData = memberDao.findData(roomId, uid);
        vo.setUtype(memberData != null ? memberData.getUtype() : 0);
        vo.setMembers(data.getMemnum());

        vo.setAdminNum(String.valueOf(memberDao.findAdminCount(roomId)));
        vo.setRoomFansNum(MatchUtils.formatDevotes(followRoomDao.getRoomFansCount(roomId)));
        vo.setRoomMembersNum(MatchUtils.formatDevotes(data.getMemnum()));
        RoomLevelData roomLevelData = roomLevelService.getRoomLevelData(roomId);
        int roomLevel = 0;
        long nowExp = 0;
        if (roomLevelData != null) {
            roomLevel = roomLevelData.getLevel();
            nowExp = roomLevelData.getExp();
        }
        vo.setRoomLevel(roomLevel);
        if (roomId.equals(RoomUtils.formatRoomId(uid))) {
            int nextLevel = roomLevel + 1;
            List<Long> expList = roomLevelConfig.getUnsafeExpList();
            int maxExpSize = expList.size() - 1;//去掉0级
            long curLevelExp = expList.get(roomLevel);
            if (nextLevel > maxExpSize) {
                nextLevel = maxExpSize;
            }
            long levelUpExp = expList.get(nextLevel);
            vo.setNextRoomLevel(nextLevel);
            vo.setRoomExp(nowExp);
            vo.setCurLevelRoomExp(curLevelExp);
            vo.setLevelUpRoomExp(levelUpExp);
            vo.setRoomLevelRate(roundHalfUpRateDiv(nowExp - curLevelExp, levelUpExp - curLevelExp));
            int nowTime = DateHelper.getNowSeconds();
            if (MatchConstant.IS_ROOM_ALLOWANCE ) {
                int sum = roomAllowanceLogDao.getSumRoomAllowanceCache(roomId, 0);
                vo.setRoomAllowanceBonus(sum > 0 ? 1 : 2);
            } else {
                vo.setRoomAllowanceBonus(0);
            }
        }
        vo.setEventSupportSwitch(RoomEventDao.EVENT_SUPPORT_SWITCH);
        vo.setEventSupportLink(RoomEventDao.EVENT_SUPPORT_LINK);
        return vo;
    }

    private double roundHalfUpRateDiv(long divisor, long dividend) {
        if (dividend == 0) {
            return 100;
        }
        if (divisor == 0) {
            return 0;
        }
        return BigDecimal.valueOf((double) divisor / dividend * 100)
                .setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 更新房间信息
     */
    public JSONObject updateRoomInfo(RoomInfoDTO req) {
        String uid = req.getUid();
        String roomId = req.getRoomId();
        String announce = req.getAnnounce();
        MongoRoomData roomData = roomDao.findData(roomId);
        if (roomData == null) {
            logger.error("Room not exist. uid={} roomId={}", uid, roomId);
            throw new CommonException(RoomHttpCode.ROOM_NOT_EXIST);
        }
        boolean isViceOwner = memberDao.isViceHostManager(uid, roomId);
        if (!RoomMemberDao.isRoomHost(roomId, uid) && !isViceOwner) {
            logger.info("You are not room owner or vice owner. uid={} roomId={}", uid, roomId);
            throw new CommonException(RoomHttpCode.NOT_ROOM_OWNER_OR_VICE_OWNER);
        }
        String detectText = req.getName() + announce + req.getTopic();
        // 脏词检测
        if (detectService.detectText(new TextDTO(detectText, DetectOriginConstant.ROOM_NAME_RELATED, uid)).getData().getIsSafe() == 0) {
            logger.info("Dirty word in room info is not allowed! uid={} roomId={} detectText={}", uid, roomId, detectText);
            commonTaskService.sendCommonTaskMq(new CommonMqTopicData(uid, roomId, "", "", CommonMqTaskConstant.ROOM_PROFILE_VIOLATION, 1));
            throw new CommonException(RoomHttpCode.ROOM_INFO_HAS_DIRTY_WORD);
        }
        int theme = req.getTheme() != null ? req.getTheme() : roomData.getTheme();
        int conquerLevel = conquerRedis.getRoomConquerLevel(roomId);
        if (req.getTheme() != null && theme != 1008611) {
            if (conquerLevel != 0) {
                logger.info("The room has been conquered, unable to change the room background temporarily. uid={} roomId={} level={}", uid, roomId, conquerLevel);
                throw new CommonException(RoomHttpCode.ROOM_HAS_BEEN_CONQUERED);
            }
        }
        MongoThemeData themeData = null;
        if (theme != 1008611 && theme != 0) {
            themeData = findRoomTheme(req, roomData, theme);
            if (themeData == null) {
                logger.info("This theme is not valid. uid={} roomId={} theme={}", uid, roomId, theme);
                throw new CommonException(RoomHttpCode.THEME_NOT_VALID);
            }
        }
        if (!StringUtils.isEmpty(announce)) {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            roomData.setAnnounce(announce);
            sendRoomAnnouncePushMsg(uid, announce, roomId, isViceOwner, actorData);
        }
        int syncFlag = buildRoomData(roomData, req, req.getTheme());
        roomDao.save(roomData);
        if (!StringUtils.isEmpty(req.getHead()) && !req.getHead().startsWith("http")) {
            asyncRoomHead(roomId, roomData.getHead(), req.getSlang());
        }
        if (syncFlag != 0) {
            sendRoomInfoChangeMsg(roomId, req.getTheme(), themeData, roomData, conquerLevel);
        }
        if (StringUtils.isEmpty(req.getHead())) {
            return null;
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("room_head", ImageUrlGenerator.generateRoomUrl(roomData.getHead()));
        return jsonObject;
    }

    private String getBgType(MongoThemeData themeData) {
        if (themeData.getTid() >= 1000) {
            return "upload";
        }
        if (themeData.getType() == 0 || themeData.getType() == 2) {
            return "default";
        } else if (themeData.getType() == 1) {
            return "VIP";
        } else {
            return "purchase";
        }
    }

    private MongoThemeData findRoomTheme(RoomInfoDTO req, MongoRoomData roomData, int theme) {
        String roomId = req.getRoomId();
        int roomTheme = roomData.getTheme();
        int micTheme = roomData.getMic_theme();
        String uid = req.getUid();
        MongoThemeData themeData = null;
        int vipLevel = getHostOrViceHostVipLevel(uid, roomId);
        RoomMicThemeData roomMicThemeData = roomMicThemeDao.selectMicThemeById(micTheme);
        if (theme != roomTheme && roomMicThemeData.getChangeTheme() <= 0){
            throw new CommonException(RoomHttpCode.BACKGROUND_NOT_CHANGE);
        }

        if (theme >= 1000) {
            if (!RoomNewService.STAFF_ROOM_SET.contains(uid) && vipLevel < 4) {
                logger.info("user vip4 expired. uid={} roomId={} theme={}", uid, roomId, theme);
                if (req.getTheme() != null) {
                    throw new CommonException(RoomHttpCode.VIP4_EXPIRED);
                } else {
                    return setDefaultTheme(roomData);
                }
            }
            UploadBackgroundData data = uploadBackgroundDao.selectOne(theme);
            if (data != null) {
                themeData = new MongoThemeData();
                themeData.setTid(data.getId());
                themeData.setMicon(data.getmIcon());
                themeData.setCicon(data.getcIcon());
                String bgUrl = ImageUrlGenerator.generateRoomTopicUrl(data.getBgUrl());
                themeData.setBgurl(bgUrl);
                themeData.setPreview(bgUrl);
            }
        } else {
            themeData = themeDao.findThemeData(theme);
            int userLevel = userLevelDao.getUserLevel(RoomUtils.getRoomHostId(roomId));
            // 使用vip主题，如果vip等级小于2且用户等级小于20，设置成默认主题
            if (!RoomNewService.STAFF_ROOM_SET.contains(uid) && themeData.getType() == 1 && vipLevel < 2 && userLevel < 20) {
                logger.info("vip2 expired. uid={} roomId={} theme={}", uid, roomId, theme);
                if (req.getTheme() != null) {
                    throw new CommonException(RoomHttpCode.VIP2_EXPIRED);
                } else {
                    return setDefaultTheme(roomData);
                }
            }
            // 购买和下发的主题过期后，设置成默认主题
            if (themeData.getType() != 0 && themeData.getType() != 1) {
                MineBackgroundData data = mineBackgroundDao.selectOne(roomId, theme);
                if (data == null || DateHelper.getNowSeconds() >= data.getEndTime()) {
                    logger.info("background expired. uid={} roomId={} theme={}", uid, roomId, theme);
                    if (req.getTheme() != null) {
                        throw new CommonException(RoomHttpCode.BACKGROUND_EXPIRED);
                    } else {
                        return setDefaultTheme(roomData);
                    }
                }
            }
        }
        return themeData;
    }

    /**
     * 设置默认主题
     */
    private MongoThemeData setDefaultTheme(MongoRoomData roomData) {
        roomData.setTheme(233);
        return DEFAULT_BACKGROUND;
    }

    private int getHostOrViceHostVipLevel(String uid, String roomId) {
        if (RoomMemberDao.isRoomHost(roomId, uid)) {
            return vipInfoDao.getIntVipLevel(uid);
        } else {
            return Math.max(vipInfoDao.getIntVipLevel(uid), vipInfoDao.getIntVipLevel(RoomUtils.getRoomHostId(roomId)));
        }
    }

    /**
     * 构建RoomData
     */
    private int buildRoomData(MongoRoomData roomData, RoomInfoDTO req, Integer theme) {
        int syncFlag = 0;
        if (req.getPrivi() != null) {
            if (RoomConstant.LIVE_ROOM_MODE == roomData.getRoomMode()) {
                throw new CommonException(RoomHttpCode.UNMODIFIABLE_MIC_PERMISSION);
            }
            int privi = req.getPrivi();
            roomData.setPrivi(privi);
            micApplyRedis.clearMicApply(req.getRoomId());
            sendRoomOptMsg(req.getRoomId(), req.getUid(), privi == 1 ? 2 : privi == 2 ? 3 : privi == 3 ? 4 : 2);
        }
        if (req.getFee() != null) {
            roomData.setFee(req.getFee());
        }
        if (req.getFeetype() != null) {
            roomData.setFeetype(req.getFeetype());
        }
        if (theme != null && theme != 1008611) {
            syncFlag = 1;
            roomData.setTheme(theme);
            roomData.setTheme_bg_type(0);
        }
        if (!StringUtils.isEmpty(req.getName())) {
            syncFlag = 1;
            roomData.setName(req.getName());
        }
        if (!StringUtils.isEmpty(req.getHead())) {
            String head = req.getHead();
            // 图片检测
            if (!head.startsWith("http")) {
                String newHead = "https://cloudcdn.qmovies.tv/" + head;
                roomData.setHead(newHead);
//                asynRoomHead(req.getRoomId(), newHead, req.getSlang());
                syncFlag = 1;
            } else if (!head.startsWith("https://imagecdn.qmovies.tv")) {
                roomData.setHead(getHttpsUrl(head));
            }
        }
        return syncFlag;
    }

    private void asyncRoomHead(String roomId, String newHead, int slang) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                String uid = RoomUtils.getRoomHostId(roomId);
//                JSONObject jsonObject = new JSONObject();
//                jsonObject.put("uid", uid);
//                jsonObject.put("type", 3);
//                jsonObject.put("url", newHead);
//                redisTaskService.pushQueue("vision_urls", jsonObject);
                if (!detectImage(newHead, DetectOriginConstant.ROOM_HEAD, uid)) {
                    roomDao.updateField(roomId, "head", baseInitData.generateRandomHead(1));
                    sendNotice(uid, newHead, slang);
                    monitorNsfwDao.save(new MonitorNsfwData(9, uid, newHead, 3, 0, DateHelper.getNowSeconds()));
                    logger.info("detectImage is not safe change to default uid:{} type_name:房间头像  url:{}", uid, newHead);
                }
            }
        });
    }

    private void sendNotice(String uid, String head, int slang) {
        OfficialData officialData;
        if (slang == SLangType.ENGLISH) {
            officialData = new OfficialData("", NOTICE_BODY, uid);
        } else {
            officialData = new OfficialData("", NOTICE_BODY_AR, uid);
        }
        officialData.setPicture(head);
        officialDao.save(officialData);
        noticeNewDao.save(new NoticeNewData(uid, officialData.get_id().toString()));
    }

    private boolean detectImage(String url, String origin, String uid) {
        try {
            if (detectService.detectImage(new ImageDTO(url, origin, uid)).getData().getIsSafe() == 1) {
                return true;
            }
            commonTaskService.sendCommonTaskMq(new CommonMqTopicData(uid, "", "", "", CommonMqTaskConstant.ROOM_PROFILE_VIOLATION, 1));
        } catch (Exception e) {
            logger.error("detect image Exception url={} uid={} origin={}", url, uid, origin, e);
        }
        return false;
    }

    private String getHttpsUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return url;
        }
        url = url.replace("http://d2fe5ao90qlpcv.cloudfront.net", "https://cdn3.qmovies.tv")
                .replace("http://d2nweiri8k4xm6.cloudfront.net", "https://cloudcdn.qmovies.tv");
        String[] urlList = url.split("\\?");
        return !StringUtils.isEmpty(urlList[0]) ? urlList[0] : url;
    }

    /**
     * 发送房间修改消息
     */
    private void sendRoomAnnouncePushMsg(String uid, String announce, String roomId, Boolean isViceOwner, ActorData actorData) {
        RoomAnnouncePushMsg msg = new RoomAnnouncePushMsg();
        UserInfoObject userInfoObject = new UserInfoObject();
        if (actorData != null) {
            userInfoObject.setUid(uid);
            userInfoObject.setName(actorData.getName());
            userInfoObject.setHead(actorData.getHead());
            userInfoObject.setViceHost(isViceOwner ? 1 : 0);
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("announce", announce);
        jsonObject.put("opt_user", userInfoObject);
        msg.fillFrom(jsonObject);
        roomWebSender.sendRoomWebMsg(roomId, "", msg, true);
    }

    /**
     * 发送房间修改消息
     */
    private void sendRoomInfoChangeMsg(String roomId, Integer themeId, MongoThemeData themeData, MongoRoomData roomData, int conquerLevel) {
        RoomInfoChangeMsg msg = new RoomInfoChangeMsg();
        msg.setRid(roomId);
        if (conquerLevel != 0) {
            ConquerActivity conquerActivity = conquerActivityDao.findOne();
            if (conquerActivity != null && !CollectionUtils.isEmpty(conquerActivity.getConfigList())) {
                Map<Integer, String> conquerThemeMap = conquerActivity.getConfigList().stream().collect(Collectors.toMap(ConquerActivity.ConfigDetail::getLevel, ConquerActivity.ConfigDetail::getTheme));
                msg.setThemeUrl(conquerThemeMap.get(conquerLevel));
            }
        } else if (themeId == null && roomData.getTheme_bg_type() == 1) {
            RoomMicThemeData micThemeData = roomMicThemeDao.selectMicThemeById(roomData.getMic_theme());
            msg.setThemeUrl(roomThemeService.getThemeBgUrl(roomData, micThemeData));
        } else {
            msg.setThemeUrl(themeData != null ? themeData.getBgurl() : "");
        }
        msg.setRoomName(roomData.getName());
        msg.setRoomHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead(), vipInfoDao.getIntVipLevelFromCache(RoomUtils.getRoomHostId(roomId))));
        roomWebSender.sendRoomWebMsg(roomId, "", msg, true);
    }

    protected String handleArbNum(String oldAccount) {
        char[] aa = oldAccount.toCharArray();
        StringBuilder newAccount = new StringBuilder();
        for (Character ch : aa) {
            newAccount.append(parseUserPwd(ch.toString()));
        }
        return newAccount.toString();
    }

    private String parseUserPwd(String userPwd) {
        String userStrPwd = "";
        try {
            // 针对阿语数字进行特殊处理
            userStrPwd = Integer.parseInt(userPwd) + "";
        } catch (NumberFormatException e) {
            logger.info("parse userPwd error. userPwd={}", userPwd);
        }
        return userStrPwd;
    }

    /**
     * 生成第三方流
     *
     * @param roomId 房间id
     * @param pwd    房间密码
     */
    public String genStreamId(String roomId, String pwd) {
        if (StringUtils.isEmpty(pwd)) {
            pwd = "you_star";
        }
        try {
            return DigestUtils.md5DigestAsHex((roomId + pwd).getBytes());
        } catch (Exception e) {
            logger.error("get streamId error roomId={} pwd={}", roomId, pwd);
            return null;
        }
    }

    /**
     * 获取房间标签
     */
    public RoomTagVO getTagList(RoomSettingsDTO req) {
        RoomTagVO vo = new RoomTagVO();
        List<RoomTags.RoomTag> tagList = SLangType.ENGLISH == req.getSlang() ? roomTags.getEnTags() : roomTags.getArTags();
        MongoRoomData data = StringUtils.isEmpty(req.getRoomId()) ? null : roomDao.findData(req.getRoomId());
        vo.setTagId(data != null ? data.getTag() : 0);
        vo.setTagList(tagList);
        return vo;
    }

    /**
     * 禁止公屏聊天
     */
    public void lockRoomChat(BanRoomChatDTO req) {
        String uid = req.getUid();
        String roomId = req.getRoom_id();
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(roomId)) {
            logger.error("ban room chat param error.uid={} roomId={}", uid, roomId);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        boolean isViceOwner = false;
        if (roomAdminRedis.isRoomAdmin(roomId, uid)) {
            isViceOwner = memberDao.isViceHostManager(uid, roomId);
        }
        boolean isOwner = RoomUtils.isHomeowner(uid, roomId);
        if (!isViceOwner && !isOwner) {
            logger.info("only room owner or vice owner can operate this. uid={} roomId={}", uid, roomId);
            throw new CommonException(RoomHttpCode.NO_PERMISSION_TO_OPERATE);
        }
        MongoRoomData data = roomDao.findData(roomId);
        if (data == null) {
            logger.error("Room not exist. roomId={}", roomId);
            throw new CommonException(RoomHttpCode.ROOM_NOT_EXIST);
        }
        if (req.getOpt() == 1) {
            data.setChat_locked(isViceOwner ? 2 : 1);
        } else {
            data.setChat_locked(0);
        }
        roomDao.save(data);
        sendRoomChatLockMsg(uid, roomId, req.getOpt(), isViceOwner);
    }

    /**
     * 发送操作公屏消息开关的消息
     */
    private void sendRoomChatLockMsg(String uid, String roomId, int opt, boolean isViceOwner) {
        OptChatPushMsg msg = new OptChatPushMsg();
        msg.fillFrom(buildMsgBody(uid, isViceOwner, opt));
        roomWebSender.sendRoomWebMsg(roomId, uid, msg, true);
    }

    /**
     * 能否发送图片开关
     */
    public void lockRoomPicture(RoomSendPicDTO req) {
        String uid = req.getUid();
        String roomId = req.getRoom_id();
        if (StringUtils.isEmpty(uid) || StringUtils.isEmpty(roomId)) {
            logger.error("ban room chat param error.uid={} roomId={}", uid, roomId);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        boolean isViceOwner = false;
        if (roomAdminRedis.isRoomAdmin(roomId, uid)) {
            isViceOwner = memberDao.isViceHostManager(uid, roomId);
        }
        boolean isOwner = RoomUtils.isHomeowner(uid, roomId);
        if (!isViceOwner && !isOwner) {
            logger.info("only room owner or vice owner can operate this. uid={} roomId={}", uid, roomId);
            throw new CommonException(RoomHttpCode.NO_PERMISSION_TO_OPERATE);
        }
        MongoRoomData data = roomDao.findData(roomId);
        if (data == null) {
            logger.error("Room not exist. roomId={}", roomId);
            throw new CommonException(RoomHttpCode.ROOM_NOT_EXIST);
        }
        data.setPic_locked(req.getOpt());
        roomDao.save(data);
        sendRoomLockSendPicMsg(uid, roomId, req.getOpt(), isViceOwner);
    }

    /**
     * 发送操作房间内发送图片开关的消息
     */
    private void sendRoomLockSendPicMsg(String uid, String roomId, int opt, boolean isViceOwner) {
        OptSendPicPushMsg msg = new OptSendPicPushMsg();
        msg.fillFrom(buildMsgBody(uid, isViceOwner, opt));
        roomWebSender.sendRoomWebMsg(roomId, "", msg, true);
    }

    private JSONObject buildMsgBody(String uid, boolean isViceOwner, int opt) {
        UserInfoObject userInfo = new UserInfoObject();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData != null) {
            userInfo.setUid(uid);
            userInfo.setName(actorData.getName());
            userInfo.setHead(actorData.getHead());
            userInfo.setViceHost(isViceOwner ? 1 : 0);
        }
        JSONObject msgBody = new JSONObject();
        msgBody.put("opt", opt);
        msgBody.put("opt_user", userInfo);
        return msgBody;
    }

    /**
     * 检查房间内发图片权限
     */
    public void sendPicCheck(RoomSendPicDTO req, boolean isRoom) {
        String uid = req.getUid();
        String roomId = req.getRoom_id();
        // v7.8 账号冻结或封禁权限控制
        Object configValue = commonConfig.getConfigValue(CommonConfig.ROOM_SEND_SWITCH);
        if (configValue != null && (double) configValue == Double.parseDouble("1") && !userMonitorDao.friendApplyPrivilege(uid)) {
            logger.info("Your account has been frozen. uid={} roomId={} ", uid, roomId);
            throw new CommonException(RoomHttpCode.ACCOUNT_HAS_BEEN_FROZEN);
        }
        if (isRoom) {
            MongoRoomData roomData = roomDao.findData(roomId);
            if (roomData == null) {
                logger.info("can not find room data. uid={} roomId={}", uid, roomId);
                throw new CommonException(HttpCode.PARAM_ERROR);
            }
            if (roomData.getPic_locked() != 0) {
                logger.info("Sending picture turned off by room owner. uid={} roomId={}", uid, roomId);
                throw new CommonException(RoomHttpCode.SEND_PIC_TURNED_OFF);
            }
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("can not find actor data. uid={} roomId={}", uid, roomId);
            throw new CommonException(HttpCode.PARAM_ERROR);
        }
        if (!StringUtils.isEmpty(actorData.getTn_id())) {
            int ctime = (int) blockRedis.getBlockRoomFileCtime(actorData.getTn_id());
            if (ctime != 0) {
                String time = DateHelper.ARABIAN.formatDateTime(new Date(ctime * 1000L));
                logger.info("Due to recent violations, you have been banned from speaking. uid={} roomId={}", uid, roomId);
                throw new CommonException(RoomHttpCode.YOU_HAVE_BEEN_BANNED, time);
            }
        }
        RoomActorDetailData detailData = roomActorCache.getData(roomId, uid, false);
        int minLevel = 20;
        int vipLevelLimit = 5;
        if (detailData.getLevel() < minLevel && detailData.getVipLevel() < vipLevelLimit) {
            logger.info("please upgrade to level 20 to send pictures. uid={} roomId={}", uid, roomId);
            if (actorData.getFb_gender() == 2) {
                throw new CommonException(RoomHttpCode.SEND_PICTURE_FEMALE_MSG);
            } else {
                throw new CommonException(RoomHttpCode.SEND_PICTURE_MALE_MSG);
            }
        }
    }

    public void updateTextLimit(BanRoomChatDTO req) {
        if (req.getLimit() < -1 || req.getLimit() > 100) {
            logger.info("update text limit param error. limit={}", req.getLimit());
            throw new CommonException(RoomHttpCode.PARAM_ERROR);
        }
        String uid = req.getUid();
        String roomId = req.getRoom_id();
        boolean isViceOwner = memberDao.isViceHostManager(uid, roomId);
        if (!RoomMemberDao.isRoomHost(roomId, uid) && !isViceOwner) {
            logger.info("You are not room owner or vice owner. uid={} roomId={}", uid, roomId);
            throw new CommonException(RoomHttpCode.NOT_ROOM_OWNER_OR_VICE_OWNER);
        }
        roomDao.updateField(roomId, "textLimit", req.getLimit());
        ActorData actorData = actorDao.getActorDataFromCache(req.getUid());
        TxtLimitPushMsg msg = new TxtLimitPushMsg();
        msg.setLevel(req.getLimit());
        UserInfoObject userInfoObject = new UserInfoObject();
        userInfoObject.setUid(req.getUid());
        userInfoObject.setName(actorData.getName());
        userInfoObject.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        userInfoObject.setViceHost(isViceOwner ? 1 : 0);
        msg.setOpt_user(userInfoObject);
        roomWebSender.sendRoomWebMsg(roomId, "", msg, true);
    }

    public void clearMsg(HttpEnvData req) {
        sendRoomOptMsg(req.getRoomId(), req.getUid(), 1);
    }

    /**
     * 发送房间设置消息
     *
     * @param roomId 房间id
     * @param uid    操作用户
     * @param opType 1清除房间公屏消息 2设置所有人可上麦 3设置会员可上麦 4设置需要申请才能上麦 5禁全麦 6取消禁全麦 7开启语聊房间 8开启直播房间
     */
    public void sendRoomOptMsg(String roomId, String uid, int opType) {
        sendRoomOptMsg(roomId, uid, opType, true);
    }

    public void sendRoomOptMsg(String roomId, String uid, int opType, boolean containMe) {
        sendRoomOptMsg(roomId, uid, opType, containMe, 0);
    }

    public void sendRoomOptMsg(String roomId, String uid, int opType, boolean containMe, int micThemeId) {
        RoomRoleData roomRoleData = memberDao.getRoleDataFromCache(roomId, uid);
        if (RoomRoleType.HOST == roomRoleData.getRole() || 1 == roomRoleData.getViceHost()) {
            RoomOptMsg msg = new RoomOptMsg();
            msg.setRole(RoomRoleType.HOST == roomRoleData.getRole() ? 1 : 2);
            msg.setOptType(opType);
            msg.setMicThemeId(micThemeId);
            logger.info("sendRoomOptMsg roomId:{} uid:{} containMe:{} msg:{}", roomId, uid, containMe, msg);
            roomWebSender.sendRoomWebMsg(roomId, containMe ? "" : uid, msg, true);
        } else {
            logger.info("You are not room owner or vice owner. roomId={} uid={}", roomId, uid);
            throw new CommonException(RoomHttpCode.NOT_ROOM_OWNER_OR_VICE_OWNER);
        }
    }

    public ConquerGameVO checkConquerGame(String roomId, String uid) {
        ConquerActivity conquerActivity = conquerActivityDao.findOne();
        if (conquerActivity == null || StringUtils.isEmpty(roomId)) {
            logger.info("activity or roomId is null. roomId={} uid={}", roomId, uid);
            throw new CommonException(RoomHttpCode.CODE_ACTIVITY_NOT_START);
        }
        int startTime = conquerActivity.getStartTime() != null ? conquerActivity.getStartTime() : 0;
        int endTime = conquerActivity.getEndTime() != null ? conquerActivity.getEndTime() : 0;
        int now = DateHelper.getNowSeconds();
        if (now < startTime || now > endTime) {
            logger.info("activity not start. roomId={} uid={} startTime={} endTime={}", roomId, uid, startTime, endTime);
            throw new CommonException(RoomHttpCode.CODE_ACTIVITY_NOT_START);
        }

        String joinUrl = conquerActivity.getJoinUrl();
        String conquerId = conquerActivity.get_id().toString();
        List<Integer> conquerNumList = conquerActivity.getConquerNumList();
        List<ConquerActivity.ConfigDetail> configList = conquerActivity.getConfigList();
        Map<Integer, ConquerActivity.ConfigDetail> configMap = configList.stream().collect(Collectors.toMap(ConquerActivity.ConfigDetail::getLevel, Function.identity()));

        ConquerGameVO vo = new ConquerGameVO();
        vo.setRoomid(roomId);
        vo.setUrl(joinUrl);

        String head = "";
        List<String> rankingList = conquerRedis.getRankingList(conquerId, roomId, 1);
        if (!CollectionUtils.isEmpty(rankingList)) {
            String oneUid = rankingList.get(0);
            ActorData actorData = actorDao.getActorData(oneUid);
            head = actorData == null ? "" : ImageUrlGenerator.generateMiniUrl(actorData.getHead());
        }
        vo.setHead(head);
        vo.setEnd_time(conquerRedis.getCountdownRoomList(conquerId, roomId));
        vo.setCurrent_value(conquerRedis.getConquerTempRoomScore(conquerId, roomId));

        int level = conquerRedis.getRoomConquerLevel(conquerId, roomId);
        if (level > 0) {
            vo.setLevel(level);
            ConquerActivity.ConfigDetail config = configMap.getOrDefault(level, null);
            vo.setTheme(StringUtils.isEmpty(config.getTheme()) ? "" : config.getTheme());
            vo.setTotal_value(conquerNumList.get(level - 1));
        } else {
            vo.setLevel(0);
            vo.setTheme("");
            vo.setTotal_value(conquerNumList.get(0));
        }
        return vo;
    }

    public PageVO<RoomActionLogVO> roomActionRecords(RoomActionLogDTO req) {
        PageVO<RoomActionLogVO> vo = new PageVO<>(new ArrayList<>());
        String aid = null;
        if (StringUtils.hasLength(req.getSearchRid())) {
            ActorData actorData = actorDao.getActorByStrRidFromDb(req.getSearchRid().trim());
            if (null == actorData) {
                return vo;
            }
            aid = actorData.getUid();
        }
        List<RoomActionLogData> pageList = roomActionLogDao.findPage(req.getRoomId(), aid, req.getActionType(), req.getPage());
        for (RoomActionLogData data : pageList) {
            ActorData fromActor = actorDao.getActorDataFromCache(data.getUid());
            ActorData toActor = actorDao.getActorDataFromCache(data.getAid());
            if (null == fromActor || null == toActor) {
                continue;
            }
            RoomActionLogVO actionLogVO = new RoomActionLogVO();
            actionLogVO.setAid(data.getAid());
            actionLogVO.setRid(String.valueOf(toActor.getRid()));
            actionLogVO.setRidData(toActor.getRidData());
            actionLogVO.setName(toActor.getName());
            actionLogVO.setHead(ImageUrlGenerator.generateRoomUserUrl(toActor.getHead()));
            actionLogVO.setDesc(formatRoomActionDesc(fromActor, data.getType(), req.getSlang()));
            actionLogVO.setCtime(data.getCtime());
            vo.getList().add(actionLogVO);
        }
        vo.setNextUrl(pageList.size() < RoomActionLogDao.PAGE_SIZE ? "" : String.valueOf(req.getPage() + 1));
        return vo;
    }

    /**
     * @param type 操作类型，1踢出房间，2加入房间黑名单，3移出房间黑名单，4踢下麦，5禁止文字聊天，6解除禁止文字聊天
     */
    private String formatRoomActionDesc(ActorData fromActor, Integer type, int slang) {
        String operator = String.valueOf(fromActor.getStrRid());
        if (1 == type) {
            return SLangType.ENGLISH == slang ? String.format(KICK_OUT_ROOM, operator) : String.format(KICK_OUT_ROOM_AR, operator);
        } else if (2 == type) {
            return SLangType.ENGLISH == slang ? String.format(ADD_ROOM_BLACKLIST, operator) : String.format(ADD_ROOM_BLACKLIST_AR, operator);
        } else if (3 == type) {
            return SLangType.ENGLISH == slang ? String.format(DEL_ROOM_BLACKLIST, operator) : String.format(DEL_ROOM_BLACKLIST_AR, operator);
        } else if (4 == type) {
            return SLangType.ENGLISH == slang ? String.format(KICK_OUT_MIC, operator) : String.format(KICK_OUT_MIC_AR, operator);
        } else if (5 == type) {
            return SLangType.ENGLISH == slang ? String.format(MUTE, operator) : String.format(MUTE_AR, operator);
        } else if (6 == type) {
            return SLangType.ENGLISH == slang ? String.format(UNMUTE, operator) : String.format(UNMUTE_AR, operator);
        }
        return "unknown operation";
    }
}
