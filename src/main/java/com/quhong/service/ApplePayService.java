package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.RefundEvent;
import com.quhong.core.AppleConsumptionReporter;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.web.HttpResponseData;
import com.quhong.core.web.WebClient;
import com.quhong.data.*;
import com.quhong.data.dto.ApplePayDTO;
import com.quhong.data.vo.ApplePayVO;
import com.quhong.enums.AppleEnvironment;
import com.quhong.enums.AppleNotificationType;
import com.quhong.enums.HttpCode;
import com.quhong.enums.PKGConstant;
import com.quhong.exception.CommonException;
import com.quhong.httpResult.PayHttpCode;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.ApplePayLogDao;
import com.quhong.mongo.data.ApplePayLogData;
import com.quhong.mq.MqSenderService;
import com.quhong.mysql.dao.AppleNotificationLogDao;
import com.quhong.mysql.dao.ApplePayDao;
import com.quhong.mysql.data.AppleNotificationLogData;
import com.quhong.mysql.data.ApplePayData;
import com.quhong.mysql.data.AppleReceiptData;
import com.quhong.mysql.data.GoodsData;
import com.quhong.mysql.mapper.ustar.AppleReceiptMapper;
import com.quhong.redis.SandboxPayRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class ApplePayService extends AbstractPayService {

    private final static Logger logger = LoggerFactory.getLogger(ApplePayService.class);
    private static final String APPLE_PAY_CHARGE = "applePayCharge";

    // 苹果支付正式验证地址
    private static final String VERIFY_RECEIPT_URL = "https://buy.itunes.apple.com/verifyReceipt";
    // 苹果支付沙箱验证地址
    private static final String SANDBOX_VERIFY_RECEIPT_URL = "https://sandbox.itunes.apple.com/verifyReceipt";
    // 苹果收据校验的秘钥
    private static final String APPLE_ACCESS_TOKEN = "582717abd2c345199ca15403b66b2ee6";
    // 苹果收据校验是沙箱的状态
    private static final int APPLE_SANDBOX_STATUS = 21007;
    private static final Map<String, String> HEADER_MAP = new HashMap<String, String>() {{
        put("Content-Type", "application/json");
    }};
    @Autowired
    private WebClient webClient;
    @Autowired
    private SandboxPayRedis payWhiteIpRedis;
    @Autowired
    private ApplePayDao applePayDao;
    @Autowired
    private AppleNotificationLogDao appleNotificationLogDao;
    @Autowired
    private ActorDao actorDao;
    @Autowired
    private AppleConsumptionReporter consumptionReporter;
    @Resource
    private ApplePayLogDao applePayLogDao;
    @Autowired
    private MqSenderService mqSenderService;
    @Autowired
    private AppleReceiptMapper appleReceiptMapper;

    public void appleNotify(AppleNotificationData notificationData, JSONObject transactionResult) {
        AppleReceiptInfo receiptInfo = new AppleReceiptInfo();
        receiptInfo.setTransaction_id(notificationData.getOriginal_transaction_id());
        receiptInfo.setOriginal_transaction_id(receiptInfo.getTransaction_id());
        if (receiptInfo.getTransaction_id() == null) {
            // pay的transactionId和original_transactionId一致
            receiptInfo.setTransaction_id(receiptInfo.getOriginal_transaction_id());
        }
        switch (notificationData.getNotification_type()) {
            case AppleNotificationType.REFUND:
                refund(notificationData, receiptInfo);
                break;
            case AppleNotificationType.CONSUMPTION_REQUEST:
                initialRefund(notificationData, receiptInfo);
                break;
            case AppleNotificationType.ONE_TIME_CHARGE:
                // 最新，内购事件
                logger.info("ONE_TIME_CHARGE: {}", transactionResult.toString());
                break;
            default:
                logger.error("no deal with notification_type={} transaction_id={}", notificationData.getNotification_type(), receiptInfo.getTransaction_id());
                break;
        }
    }

    private void initialRefund(AppleNotificationData notificationData, AppleReceiptInfo receiptInfo) {
        if (notificationData.getEnvironment().equals(AppleEnvironment.SANDBOX)) {
            logger.info("initial refund is sandbox.transactionId={}", receiptInfo.getTransaction_id());
            return;
        }
        if (appleNotificationLogDao.getData(receiptInfo.getTransaction_id(), notificationData.getNotification_type()) != null) {
            return;
        }
        ApplePayData payData = applePayDao.getData(receiptInfo.getTransaction_id());
        if (payData == null) {
            logger.error("initial refund. can not find payData. transactionId={}", receiptInfo.getTransaction_id());
            sendWarn("initial refund error. do not find transaction", "transaction_id=" + receiptInfo.getTransaction_id());
            return;
        }
        logger.error("user initial refund.transactionId={} uid={}", receiptInfo.getTransaction_id(), payData.getUserid());
        notifyLog(notificationData, payData, receiptInfo);
        consumptionReporter.sendConsumptionInformation(notificationData, receiptInfo, payData);
        sendMonitor(payData, true, "");
    }

    private void refund(AppleNotificationData notificationData, AppleReceiptInfo receiptInfo) {
        if (notificationData.getEnvironment().equals(AppleEnvironment.SANDBOX)) {
            logger.info("refund is sandbox.transactionId={}", receiptInfo.getTransaction_id());
            return;
        }
        if (appleNotificationLogDao.getData(receiptInfo.getTransaction_id(), notificationData.getNotification_type()) != null) {
            return;
        }
        ApplePayData payData = applePayDao.getData(receiptInfo.getTransaction_id());
        if (payData == null) {
            logger.error("refund. can not find payData. productId={}.transactionId={}", receiptInfo.getProduct_id(), receiptInfo.getTransaction_id());
            sendWarn("refund error. do not find transaction", "transaction_id=" + receiptInfo.getTransaction_id());
            return;
        }
        logger.error("user refund.transactionId={} uid={}", receiptInfo.getTransaction_id(), payData.getUserid());
        int deductDiamonds = 0;
        String refundDesc = "";
        if (payData.getFstatus() == 1) {
            deductDiamonds = deductRefundBeans(payData.getUserid(), payData.getProductId(), payData.getTransactionId(), "refund", "refund from apple");
            RechargeInfo rechargeInfo = new RechargeInfo(payData.getUserid(), receiptInfo.getTransaction_id());
            rechargeInfo.setRechargeItem(RECHARGE_ITEM_MAP.get(APPLE_PAY_CHARGE));
            mqSenderService.sendRefundOrderToMq(MqSenderService.REFUND_APPLE_ROUTE_KEY, rechargeInfo);
            refundDesc = "deduct diamonds " + deductDiamonds;
        } else {
            refundDesc = "order has problem";
        }
        notifyLog(notificationData, payData, receiptInfo);
        // 发送退款mq消息
        // mqSenderService.sendRefundOrderToMq(new RechargeInfo(payData.getUserid(), receiptInfo.getTransaction_id()));
        sendMonitor(payData, false, refundDesc);
        // 退款事件
        GoodsData goodsData = goodsDao.getGoodsMap().get(payData.getProductId());
        RefundEvent event = new RefundEvent();
        event.setUid(payData.getUserid());
        event.setPay_title(APPLE_PAY_CHARGE);
        event.setPay_type(2);
        event.setRecharge_time((int) payData.getCtime());
        event.setRecharge_money(goodsData.getShowInfoData().getOrigin().replace("$", ""));
        event.setRefund_type(2);
        event.setShould_deduct(goodsData.getBeans());
        event.setActual_deduct(deductDiamonds);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    private void sendMonitor(ApplePayData payData, boolean initial, String desc) {
        try {
            String title;
            if (initial) {
                title = "apple用户初始化退款";
            } else {
                title = "apple用户退款";
            }
            StringBuilder contentBuilder = new StringBuilder();
            contentBuilder.append("uid:");
            contentBuilder.append(payData.getUserid());
            ActorData actorData = actorDao.getActorData(payData.getUserid());
            if (actorData != null) {
                contentBuilder.append(", rid:");
                contentBuilder.append(actorData.getRid());
            }
            contentBuilder.append(", productId:");
            contentBuilder.append(payData.getProductId());
            contentBuilder.append(" purchase:");
            contentBuilder.append(desc == null ? "" : desc);
            monitorSender.info("diamonds", title, contentBuilder.toString());
        } catch (Exception e) {
            logger.error("send monitor error. uid={} {}", payData.getUserid(), e.getMessage(), e);
        }
    }

    private AppleNotificationLogData notifyLog(AppleNotificationData notificationData, ApplePayData payData, AppleReceiptInfo receiptInfo) {
        try {
            AppleNotificationLogData logData = new AppleNotificationLogData();
            logData.setUserId(payData.getUserid());
            logData.setTransactionId(payData.getTransactionId());
            logData.setOriginalTransactionId(receiptInfo.getOriginal_transaction_id());
            logData.setExpireTime(0);
            if (receiptInfo.getPurchase_date_ms() > 0) {
                logData.setPurchaseTime(receiptInfo.getPurchase_date_ms());
            } else {
                logData.setPurchaseTime(payData.getCtime());
            }
            logData.setCtime(DateHelper.getNowSeconds());
            logData.setProductId(payData.getProductId());
            logData.setProductType(0);
            logData.setNotificationType(notificationData.getNotification_type());
            appleNotificationLogDao.insert(logData);
            return logData;
        } catch (Exception e) {
            logger.error("apple notify log error. {}", e.getMessage(), e);
        }
        return null;
    }

    public AppleReceiptInfo getLatestReceipts(List<AppleReceiptInfo> receiptInfoList) {
        if (receiptInfoList == null) {
            return null;
        }
        long maxTime = 0;
        AppleReceiptInfo latestReceipt = null;
        try {
            for (AppleReceiptInfo receiptInfo : receiptInfoList) {
                long expiresDate = receiptInfo.getExpires_date_ms() / 1000L;
                if (maxTime == 0) {
                    maxTime = expiresDate;
                    latestReceipt = receiptInfo;
                } else if (maxTime < expiresDate) {
                    maxTime = expiresDate;
                    latestReceipt = receiptInfo;
                }
            }
            if (latestReceipt == null) {
                return null;
            }
        } catch (Exception e) {
            logger.error("apple get last receipt size={} {}", receiptInfoList.size(), e.getMessage(), e);
        }
        return latestReceipt;
    }

    /**
     * <a href="https://developer.apple.com/cn/documentation/storekit/original_api_for_in-app_purchase/">App 内购买项目的原始 API</a>
     */
    public ApplePayVO applePayVerify(ApplePayDTO payDTO) {
        try (DistributeLock lock = new DistributeLock(payDTO.getTransaction_id(), 30)) {
            lock.lock();
            // 订单验证(新)
            verifyTransactionId(payDTO);
            // 订单验证
            Integer orderId = getApplePayDataAndCheck(payDTO);
            // 请求Apple服务器验证交易是否有效
            AppleVerifyData appleVerify = sendAppleVerify(payDTO.getUid(), payDTO.getReceipt_data(), payDTO.getTransaction_id(), true);
            boolean sandbox = APPLE_SANDBOX_STATUS == appleVerify.getStatus();
            if (null == orderId) {
                // 保存订单
                orderId = saveOrder(payDTO, sandbox, appleVerify.getReceipt().getReceipt_creation_date_ms());
            }
            // 订单校验
            String productId = verifyCheck(appleVerify, payDTO);
            int balance = 0;
            if (appleVerify.isVerifyCheck()) {
                // 下发商品
                balance = chargeBeans(payDTO.getTransaction_id(), productId, payDTO.getUid(), APPLE_PAY_CHARGE, "buy " + productId, payDTO.getTransaction_id(), payDTO.getCouponId());
            }
            // 保存订单
            applePayDao.finishOrder(payDTO.getUid(), orderId, sandbox);
            applePayLogDao.updateStatus(payDTO.getTransaction_id(), 1);
            // 首充检查
            firstRechargeService.checkFirstRecharge(payDTO.getUid(), 2, productId);
            // 保存用户最新的收据，用于手动补单校验
            saveOrUpdateAppleReceipt(payDTO.getUid(), payDTO.getReceipt_data());
            return new ApplePayVO(0, 0, sandbox ? 1 : 0, balance);
        }
    }

    private void saveOrUpdateAppleReceipt(String uid, String receiptData) {
        try {
            AppleReceiptData appleReceiptData = appleReceiptMapper.selectOne(new QueryWrapper<AppleReceiptData>().eq("uid", uid));
            if (null == appleReceiptData) {
                appleReceiptMapper.insert(new AppleReceiptData(uid, receiptData));
            } else {
                appleReceiptData.setReceipt(receiptData);
                appleReceiptData.setMtime(DateHelper.getNowSeconds());
                appleReceiptMapper.updateById(appleReceiptData);
            }
        } catch (Exception e) {
            logger.error("saveOrUpdateAppleReceipt error. uid={} {}", uid, e.getMessage(), e);
        }
    }

    private String verifyCheck(AppleVerifyData appleVerify, ApplePayDTO payDTO) {
        String productId = null;
        if (PKGConstant.IOS_MAIN.equals(appleVerify.getReceipt().getBundle_id())) {
            for (AppleReceiptInfo appleReceiptInfo : appleVerify.getReceipt().getIn_app()) {
                if (payDTO.getTransaction_id().equals(appleReceiptInfo.getTransaction_id())) {
                    productId = appleReceiptInfo.getProduct_id();
                    break;
                } else {
                    if (null == applePayLogDao.findByTransactionId(appleReceiptInfo.getTransaction_id())) {
                        logger.info("find apple lost order. uid={} transactionId={} productId={}", payDTO.getUid(), appleReceiptInfo.getTransaction_id(), appleReceiptInfo.getProduct_id());
                        sendWarn("发现苹果丢单", JSON.toJSONString(appleReceiptInfo));
                    }
                }
            }
        }
        if (appleVerify.getStatus() != 0 || null == productId) {
            logger.error("apple verify fail. uid={} receipt_data={} verify_data={}", payDTO.getUid(), payDTO.getReceipt_data(), JSON.toJSONString(appleVerify));
            sendWarn("苹果订单验证失败", JSON.toJSONString(payDTO));
            throw new CommonException(PayHttpCode.APPLE_VERIFY_FAIL);
        }
        return productId;
    }

    private int saveOrder(ApplePayDTO payDTO, boolean sandbox, String receipt_creation_date_ms) {
        ApplePayData applePayData = new ApplePayData();
        applePayData.setUserid(payDTO.getUid());
        applePayData.setProductId(payDTO.getProduct_id());
        applePayData.setTransactionId(payDTO.getTransaction_id());
        applePayData.setFstatus(sandbox ? 10 : 0);
        applePayData.setReceiptData("");
        applePayData.setReceiptDataMd5(DigestUtils.md5DigestAsHex((payDTO.getReceipt_data()).getBytes()));
        applePayData.setIsAc(payDTO.getIs_ac() == null ? 0 : payDTO.getIs_ac());
        int nowSeconds = DateHelper.getNowSeconds();
        applePayData.setCtime(nowSeconds);
        applePayData.setMtime(nowSeconds);
        try {
            applePayData.setPtime(Long.parseLong(receipt_creation_date_ms) / 1000);
        } catch (Exception e) {
            applePayData.setPtime(nowSeconds);
        }
        try {
            applePayDao.save(applePayData);
            return applePayData.getRid();
        } catch (Exception e) {
            logger.error("saveOrder error uid={} tid={} rid={}", payDTO.getUid(), payDTO.getTransaction_id(), payDTO.getReceipt_data());
            applePayLogDao.updateStatus(payDTO.getTransaction_id(), 4);
            sendWarn("苹果订单保存失败", JSON.toJSONString(applePayData));
            throw new CommonException(PayHttpCode.APPLE_SAVE_ORDER_FAIL);
        }
    }

    private Integer getApplePayDataAndCheck(ApplePayDTO payDTO) {
        try {
            ApplePayData applePayData = applePayDao.getData(payDTO.getTransaction_id());
            if (null != applePayData) {
                int fstatus = applePayData.getFstatus();
                if (fstatus == 1 || fstatus == 2 || fstatus == 11 || fstatus == 12) {
                    logger.error("The order has already existed fstatus={} uid={}", fstatus, payDTO.getUid());
                    applePayLogDao.updateStatus(payDTO.getTransaction_id(), 2);
                    throw new CommonException(new HttpCode(3, "The order has already existed[fstatus=" + fstatus + "]"));
                }
                return applePayData.getRid();
            } else {
                return null;
            }
        } catch (Exception e) {
            logger.error("getApplePayData error uid={} tid={} rid={}", payDTO.getUid(), payDTO.getTransaction_id(), payDTO.getReceipt_data());
            applePayLogDao.updateStatus(payDTO.getTransaction_id(), 4);
            throw new CommonException(PayHttpCode.LOCAL_SERVER_ERROR);
        }
    }

    private void verifyTransactionId(ApplePayDTO payDTO) {
        ApplePayLogData logData = applePayLogDao.findByTransactionId(payDTO.getTransaction_id());
        if (null != logData) {
            int fstatus = logData.getFstatus();
            //  这些异常还可以再次验证，0不验证，是因为可能有一些未知错误，正常流程都会写fsatus
            if (fstatus == 3 || fstatus == 4 || fstatus == 5 || fstatus == 6) {
                // 这里要新赋值0，代表新的一个流程的开始
                logData.setFstatus(0);
                applePayLogDao.save(logData);
            } else if (fstatus != 0) {
                logger.info("transaction_id is exist uid={} product_id={} transaction_id={} fstatus={}",
                        payDTO.getUid(), payDTO.getProduct_id(), payDTO.getTransaction_id(), fstatus);
                throw new CommonException(PayHttpCode.APPLE_RE_VERIFY);
            }
        } else {
            logData = new ApplePayLogData();
            logData.setUid(payDTO.getUid());
            logData.setProduct_id(payDTO.getProduct_id());
            logData.setTransaction_id(payDTO.getTransaction_id());
            logData.setFstatus(0);
            logData.setCtime(DateHelper.getNowSeconds());
            applePayLogDao.save(logData);
            if (null == logData.get_id()) {
                logger.error("save ApplePayLogData data error. uid={}", payDTO.getUid());
                sendWarn("苹果订单验证记录保存失败", JSON.toJSONString(payDTO));
                throw new CommonException(PayHttpCode.LOCAL_SERVER_ERROR);
            }
        }
    }

    /**
     * 请求Apple服务器验证交易是否有效
     * <a href="https://developer.apple.com/documentation/appstorereceipts/verifyreceipt">verifyReceipt</a>
     * 0     收据有效
     * 21000 未使用HTTP POST请求方法向App Store发送请求。
     * 21001 此状态代码不再由App Store发送。
     * 21002 receipt-data属性中的数据格式错误，或者服务遇到了临时问题。再试一次。
     * 21003 收据无法认证。
     * 21004 您提供的共享密钥与您帐户的文件共享密钥不匹配。
     * 21005 收据服务器暂时无法提供收据。再试一次。
     * 21006 该收据有效，但订阅已过期。当此状态码返回到您的服务器时，收据数据也会被解码并作为响应的一部分返回。仅针对自动续订的iOS 6样式的交易收据返回。
     * 21007 该收据来自测试环境，但是已发送到生产环境以进行验证。
     * 21008 该收据来自生产环境，但是已发送到测试环境以进行验证。
     * 21009 内部数据访问错误。稍后再试。
     * 21010 找不到或删除了该用户帐户。
     *
     * @param uid         用户id
     * @param receiptData 收据
     * @return 苹果服务器异常、正式环境非白名单沙盒抛出异常
     */
    public AppleVerifyData sendAppleVerify(String uid, String receiptData, String transactionId, boolean sandboxWarn) {
        JSONObject data = new JSONObject();
        data.put("receipt-data", receiptData);
        data.put("password", APPLE_ACCESS_TOKEN);
        logger.info("apple start verify. uid={}", uid);
        HttpResponseData<String> httpData = webClient.sendRestfulPostNoReuse(VERIFY_RECEIPT_URL, data.toJSONString(), HEADER_MAP, 5);
        if (null == httpData || StringUtils.isEmpty(httpData.getBody())) {
            logger.error("apple receipt verify error. body is empty. uid={} body={}", uid, JSON.toJSONString(httpData));
            sendWarn("苹果订单验证-服务器调用失败", "uid=" + uid + ", transactionId=" + transactionId, "ustar_java_exception");
            throw new CommonException(PayHttpCode.APPLE_VERIFY_SERVER_ERROR);
        }
        String body = httpData.getBody();
        logger.info("apple verify result={}. uid={}", body, uid);
        AppleVerifyData verifyData = JSON.parseObject(body, AppleVerifyData.class);
        if (null == verifyData || null == verifyData.getStatus()) {
            logger.error("apple receipt verify error. status is empty. uid={} body={}", uid, JSON.toJSONString(httpData));
            sendWarn("苹果订单验证-服务器调用失败", "uid=" + uid + ", transactionId=" + transactionId, "ustar_java_exception");
            throw new CommonException(PayHttpCode.SERVER_ERROR);
        }
        if (APPLE_SANDBOX_STATUS == verifyData.getStatus()) {
            logger.info("apple verify sandbox. uid={}", uid);
            httpData = webClient.sendRestfulPostNoReuse(SANDBOX_VERIFY_RECEIPT_URL, JSON.toJSONString(data), HEADER_MAP, 5);
            body = httpData.getBody();
            if (StringUtils.isEmpty(body)) {
                logger.info("apple verify sandbox error, status={} body={} uid={}", httpData.getStatus(), body, uid);
                throw new CommonException(PayHttpCode.APPLE_VERIFY_SERVER_ERROR);
            }
            logger.info("apple sandbox verify result={}. uid={}", body, uid);
            verifyData = JSON.parseObject(body, AppleVerifyData.class);
            if (checkCanSandbox(uid)) {
                if (sandboxWarn) {
                    sendWarn("苹果沙盒订单", "uid=" + uid);
                }
            } else {
                logger.error("apple verify sandbox error can not verify in sandbox. uid={}", uid);
                // sendWarn("苹果沙盒订单来自非白名单用户！", "uid=" + uid);
                verifyData.setVerifyCheck(false);
            }
        }
        if (null == verifyData || null == verifyData.getReceipt()
                || StringUtils.isEmpty(verifyData.getReceipt().getBundle_id())
                || CollectionUtils.isEmpty(verifyData.getReceipt().getIn_app())) {
            logger.error("verifyData is null. The Apple server is currently unavailable. uid={} receiptData={}", uid, receiptData);
            throw new CommonException(PayHttpCode.APPLE_VERIFY_SERVER_ERROR);
        }
        return verifyData;
    }

    private boolean checkCanSandbox(String uid) {
        if (ServerConfig.isNotProduct()) {
            // 非生产环境，可以验证沙箱环境
            return true;
        }
        // 生产环境，在白名单中，可以验证沙箱环境
        return payWhiteIpRedis.iosSandbox(uid);
    }

    @Override
    void onChargeFailure(String bizId, String productId, String uid) {
        applePayLogDao.updateStatus(bizId, 6);
        throw new CommonException(PayHttpCode.LOCAL_SERVER_ERROR);
    }

    public String findLostOrder(String uid) {
        AppleReceiptData appleReceiptData = appleReceiptMapper.selectOne(new QueryWrapper<AppleReceiptData>().eq("uid", uid));
        if (null == appleReceiptData) {
            return "cannot find receipt data.";
        }
        AppleVerifyData appleVerify = sendAppleVerify(appleReceiptData.getUid(), appleReceiptData.getReceipt(), null, false);
        if (null == appleVerify) {
            return "apple verify data error.";
        }
        JSONObject jsonObject = new JSONObject();
        List<AppleReceiptInfo> successList = new ArrayList<>();
        if (PKGConstant.IOS_MAIN.equals(appleVerify.getReceipt().getBundle_id()) && appleVerify.isVerifyCheck()) {
            for (AppleReceiptInfo appleReceiptInfo : appleVerify.getReceipt().getIn_app()) {
                logger.info("find apple lost order. uid={} transactionId={} productId={}", uid, appleReceiptInfo.getTransaction_id(), appleReceiptInfo.getProduct_id());
                ApplePayDTO dto = new ApplePayDTO();
                dto.setUid(uid);
                dto.setProduct_id(appleReceiptInfo.getProduct_id());
                dto.setTransaction_id(appleReceiptInfo.getTransaction_id());
                dto.setReceipt_data("findLostOrder");
                if (applePatch(dto, APPLE_SANDBOX_STATUS == appleVerify.getStatus(), appleVerify.getReceipt().getReceipt_creation_date_ms())) {
                    successList.add(appleReceiptInfo);
                    sendWarn("手动补单成功", JSON.toJSONString(appleReceiptInfo));
                }
            }
        }
        jsonObject.put("successPatch", successList);
        return jsonObject.toJSONString();
    }

    private boolean applePatch(ApplePayDTO dto, boolean sandbox, String receipt_creation_date_ms) {
        try {
            logger.info("start apple patch. uid={} dto={}", dto.getUid(), JSON.toJSON(dto));
            verifyTransactionId(dto);
            // 订单验证
            Integer orderId = getApplePayDataAndCheck(dto);
            if (null == orderId) {
                // 保存订单
                orderId = saveOrder(dto, sandbox, receipt_creation_date_ms);
            }
            // 下发商品
            chargeBeans(dto.getTransaction_id(), dto.getProduct_id(), dto.getUid(), APPLE_PAY_CHARGE, "buy " + dto.getProduct_id(), dto.getTransaction_id(), 0);
            // 保存订单
            applePayDao.finishOrder(dto.getUid(), orderId, sandbox);
            applePayLogDao.updateStatus(dto.getTransaction_id(), 1);
            return true;
        } catch (CommonException e) {
            logger.info("apple patch error. uid={} dto={} {}", dto.getUid(), JSON.toJSONString(dto), JSON.toJSONString(e.getHttpCode()));
            return false;
        }
    }
}
