package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.constant.FcmMsgTypeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.CountryData;
import com.quhong.data.PushMsgData;
import com.quhong.data.dto.SendFcmDTO;
import com.quhong.enums.LogType;
import com.quhong.feign.IFcmService;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mysql.dao.DAUDao;
import com.quhong.redis.DataRedisBean;
import com.quhong.redis.FcmMsgRedis;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class UserNoActivePushService{

    private static final Logger logger = LoggerFactory.getLogger(UserNoActivePushService.class);
    private static final Logger msgLogger = LoggerFactory.getLogger(LogType.MESSAGE_LOG);
    private static List<PushMsgData> PUSH_MSG_LIST = new ArrayList<>();
    static {
        PUSH_MSG_LIST.add(new PushMsgData("Hello! user_name", "مرحبًا! user_name", "Your friends are waiting for you! Enter the room today and get a surprise.", "أصدقاؤك في انتظارك! ادخل الغرفة اليوم واحصل على مفاجأة."));
        PUSH_MSG_LIST.add(new PushMsgData("recommend_user_name Just mentioned you!", "recommend_user_name ذكرَك للتو!", "They're all waiting for you to come back and chat, Come see what they've been saying!", "الجميع ينتظر عودتك للدردشة، تعال وشاهد ماذا قالوا!"));
        PUSH_MSG_LIST.add(new PushMsgData("Your voice partner is now online!", "شريكك في الصوت متصل الآن!", "Waiting for you in the room for voice chat. Click to join→", "ينتظرك في الغرفة للدردشة الصوتية. انقر للانضمام←"));
        PUSH_MSG_LIST.add(new PushMsgData("Three new friends followed you this week.", "ثلاثة أصدقاء جدد تابعوك هذا الأسبوع.", "Don't keep them waiting too long! 💬", "لا تجعلهم ينتظرون طويلاً! 💬"));
        PUSH_MSG_LIST.add(new PushMsgData("While you were away, a secret room was discovered...", "أثناء غيابك، تم اكتشاف غرفة سرية...", "Click to view🔍", "انقر لعرض🔍"));
        PUSH_MSG_LIST.add(new PushMsgData("There is an unread voice message from a mystery person.", "هناك رسالة صوتية غير مقروءة من شخص غامض.", "Come and listen to who it is! 🎧", "تعال واستمع من هو! 🎧"));
        PUSH_MSG_LIST.add(new PushMsgData("The room you are following is discussing a topic about you", "الغرفة التي تتابعها تدردش موضوعًا عنك", "Come and take a look👀", "تعال وشاهد بسرعة 👀"));
        PUSH_MSG_LIST.add(new PushMsgData("We miss your voice!", "نفتقد صوتك!", "Tonight at 9, same place — don't miss it~", "الليلة الساعة 9، نفس المكان — لا تفوتها~"));
        PUSH_MSG_LIST.add(new PushMsgData("Hot Topics this week", "المواضيع الرائعة لهذا الأسبوع", "It's the final day of the PK competition, and your opinion is crucial! 🗳️", "هو اليوم الأخير من مسابقة، ورأيك مهم جدًا! 🗳️"));
        PUSH_MSG_LIST.add(new PushMsgData("Based on your preferences, we recommend the new room \"Emotional Radio.\"", "بناءً على تفضيلاتك، نوصي بالغرفة الجديدة \"الراديو العاطفي.\"", "The host's voice is very soothing 🎶", "صوت المُقدّم مريح جداً 🎶"));
    }



    @Resource
    private FcmMsgRedis fcmMsgRedis;
    @Resource
    private ActorDao actorDao;
    @Resource
    private IFcmService iFcmService;
    @Resource
    private DAUDao dauDao;
    @Resource
    private CountryQuery countryQuery;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;

    public void noActivePush() {
        try {
            logger.info("开始执行不活跃用户推送任务");

            // 获取7天内和3天内的日活用户
            Set<String> sevenDaysActiveUsers = dauDao.getActiveUserSet(7);
            Set<String> threeDaysActiveUsers = dauDao.getActiveUserSet(3);

            // 计算差集：7天内活跃但3天内不活跃的用户
            Set<String> targetUsers = new HashSet<>(sevenDaysActiveUsers);
            targetUsers.removeAll(threeDaysActiveUsers);

            if (CollectionUtils.isEmpty(targetUsers)) {
                logger.info("没有符合条件的不活跃用户");
                return;
            }

            logger.info("找到{}个符合条件的不活跃用户", targetUsers.size());

            // 按时区分组用户并进行推送
            processUsersByTimezone(targetUsers);

        } catch (Exception e) {
            logger.error("执行不活跃用户推送任务失败", e);
        }
    }

    /**
     * 按时区分组用户并进行推送
     */
    private void processUsersByTimezone(Set<String> targetUsers) {
        // 按时区分组用户
        Map<String, List<String>> usersByTimezone = new HashMap<>();

        for (String uid : targetUsers) {
            try {
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                if (actorData == null || StringUtils.isEmpty(actorData.getIp())) {
                    continue;
                }

                // 根据IP获取国家信息
                CountryData countryData = countryQuery.find(actorData.getIp());
                if (countryData == null || StringUtils.isEmpty(countryData.getCode())) {
                    continue;
                }

                // 根据国家代码获取时区
                String timezone = getTimezoneByCountryCode(countryData.getCode());
                usersByTimezone.computeIfAbsent(timezone, k -> new ArrayList<>()).add(uid);

            } catch (Exception e) {
                logger.error("处理用户{}时区信息失败", uid, e);
            }
        }

        // 对每个时区的用户检查推送时间
        for (Map.Entry<String, List<String>> entry : usersByTimezone.entrySet()) {
            String timezone = entry.getKey();
            List<String> users = entry.getValue();

            if (shouldPushAtCurrentTime(timezone)) {
                pushToUsers(users);
            }
        }
    }

    /**
     * 根据国家代码获取时区
     */
    private String getTimezoneByCountryCode(String countryCode) {
        // 主要国家时区映射
        Map<String, String> countryTimezoneMap = new HashMap<>();

        // 海湾国家 (GMT+3)
        countryTimezoneMap.put("SA", "Asia/Riyadh");     // 沙特阿拉伯
        countryTimezoneMap.put("AE", "Asia/Dubai");      // 阿联酋
        countryTimezoneMap.put("KW", "Asia/Kuwait");     // 科威特
        countryTimezoneMap.put("QA", "Asia/Qatar");      // 卡塔尔
        countryTimezoneMap.put("BH", "Asia/Bahrain");    // 巴林
        countryTimezoneMap.put("OM", "Asia/Muscat");     // 阿曼
        countryTimezoneMap.put("IQ", "Asia/Baghdad");    // 伊拉克
        countryTimezoneMap.put("JO", "Asia/Amman");      // 约旦
        countryTimezoneMap.put("LB", "Asia/Beirut");     // 黎巴嫩
        countryTimezoneMap.put("SY", "Asia/Damascus");   // 叙利亚

        // 其他主要国家
        countryTimezoneMap.put("EG", "Africa/Cairo");    // 埃及 (GMT+2)
        countryTimezoneMap.put("TR", "Europe/Istanbul"); // 土耳其 (GMT+3)
        countryTimezoneMap.put("IN", "Asia/Kolkata");    // 印度 (GMT+5:30)
        countryTimezoneMap.put("PK", "Asia/Karachi");    // 巴基斯坦 (GMT+5)
        countryTimezoneMap.put("BD", "Asia/Dhaka");      // 孟加拉国 (GMT+6)
        countryTimezoneMap.put("US", "America/New_York"); // 美国东部 (GMT-5)
        countryTimezoneMap.put("GB", "Europe/London");   // 英国 (GMT+0)
        countryTimezoneMap.put("DE", "Europe/Berlin");   // 德国 (GMT+1)
        countryTimezoneMap.put("FR", "Europe/Paris");    // 法国 (GMT+1)
        countryTimezoneMap.put("CN", "Asia/Shanghai");   // 中国 (GMT+8)
        countryTimezoneMap.put("JP", "Asia/Tokyo");      // 日本 (GMT+9)

        return countryTimezoneMap.getOrDefault(countryCode, "Asia/Riyadh"); // 默认使用沙特时区
    }

    /**
     * 检查当前时间是否为推送时间（用户当地时间20:00或21:00）
     */
    private boolean shouldPushAtCurrentTime(String timezone) {
        try {
            ZoneId zoneId = ZoneId.of(timezone);
            ZonedDateTime userLocalTime = ZonedDateTime.now(zoneId);
            int hour = userLocalTime.getHour();
            int minute = userLocalTime.getMinute();

            // 检查是否为20:00或21:00（允许0-5分钟的误差）
            return (hour == 20 || hour == 21) && minute <= 5;

        } catch (Exception e) {
            logger.error("检查推送时间失败，时区：{}", timezone, e);
            return false;
        }
    }

    /**
     * 向用户列表发送推送
     */
    private void pushToUsers(List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        logger.info("开始向{}个用户发送不活跃推送", userIds.size());

        for (String uid : userIds) {
            try {
                // 检查是否已经推送过（24小时内）
                if (hasAlreadyPushed(uid)) {
                    continue;
                }

                // 随机选择推送消息
                PushMsgData pushMsg = getRandomPushMessage();

                // 构建推送参数
                Map<String, String> paramMap = new HashMap<>();
                paramMap.put(FcmMsgTypeConstant.FCM_MESSAGE_ID_KEY, new ObjectId().toString());
                paramMap.put(FcmMsgTypeConstant.FCM_ORIGIN_KEY, FcmMsgTypeConstant.FCM_SYSTEM);
                paramMap.put(FcmMsgTypeConstant.FCM_SUB_TYPE_KEY, FcmMsgTypeConstant.FCM_SUB_4);
                paramMap.put(FcmMsgTypeConstant.FCM_TITLE_KEY, pushMsg.getTitleEn());
                paramMap.put(FcmMsgTypeConstant.STEP_ACTION_TYPE_KEY, FcmMsgTypeConstant.VOICE_ROOM);

                JSONObject actionConfig = new JSONObject();
                actionConfig.put(FcmMsgTypeConstant.ACTION_VALUE_KEY, "");
                paramMap.put(FcmMsgTypeConstant.ACTION_CONFIG_KEY, actionConfig.toString());

                // 构建FCM推送DTO
                SendFcmDTO sendFcmDTO = new SendFcmDTO();
                sendFcmDTO.setParamMap(paramMap);
                sendFcmDTO.setToUidSet(Collections.singleton(uid));
                sendFcmDTO.setTitle(pushMsg.getTitleEn());
                sendFcmDTO.setTitleAr(pushMsg.getTitleAr());
                sendFcmDTO.setBody(pushMsg.getBodyEn());
                sendFcmDTO.setBodyAr(pushMsg.getBodyAr());

                // 发送推送
                iFcmService.sendFcmMsg(sendFcmDTO);

                // 设置推送记录（24小时过期）
                markAsPushed(uid);

                msgLogger.info("不活跃用户推送发送成功，uid={}", uid);

            } catch (Exception e) {
                logger.error("向用户{}发送不活跃推送失败", uid, e);
            }
        }
    }

    /**
     * 随机获取推送消息
     */
    private PushMsgData getRandomPushMessage() {
        if (CollectionUtils.isEmpty(PUSH_MSG_LIST)) {
            // 默认消息
            return new PushMsgData("Hello! user_name", "مرحبًا! user_name",
                    "Your friends are waiting for you! Enter the room today and get a surprise.",
                    "أصدقاؤك في انتظارك! ادخل الغرفة اليوم واحصل على مفاجأة.");
        }

        Random random = new Random();
        return PUSH_MSG_LIST.get(random.nextInt(PUSH_MSG_LIST.size()));
    }

    /**
     * 检查用户是否已经推送过
     */
    private boolean hasAlreadyPushed(String uid) {
        try {
            String key = getNoActivePushKey(uid);
            String value = clusterRedis.opsForValue().get(key);
            return !StringUtils.isEmpty(value);
        } catch (Exception e) {
            logger.error("检查用户推送状态失败，uid={}", uid, e);
            return false;
        }
    }

    /**
     * 标记用户已推送
     */
    private void markAsPushed(String uid) {
        try {
            String key = getNoActivePushKey(uid);
            clusterRedis.opsForValue().set(key, "1", 24 * 3600, TimeUnit.SECONDS);
        } catch (Exception e) {
            logger.error("标记用户推送状态失败，uid={}", uid, e);
        }
    }

    /**
     * 获取不活跃推送的Redis key
     */
    private String getNoActivePushKey(String uid) {
        return "str:no_active_push:" + uid;
    }


    // private void noActivePush() {
    //     try {
    //         int pushFriendUser = fcmMsgRedis.getPushFriendUserStatus(uid);
    //         if (roomData == null){
    //             return;
    //         }
    //         if(pushFriendUser <= 0){
    //             long millis = System.currentTimeMillis();
    //             List<FriendsData> friendsDataList = friendsDao.findAllFriend(uid, false);
    //             int currentTime = DateHelper.getNowSeconds();
    //             int lastSevenTime = currentTime - 7 * 86400;
    //             List<String> friendUidList = friendsDataList.stream().filter(item -> item.getCtime() >= lastSevenTime).map(friend -> uid.equals(friend.getUidSecond()) ? friend.getUidFirst() : friend.getUidSecond()).collect(Collectors.toList());
    //             if (!CollectionUtils.isEmpty(friendUidList)){
    //                 fcmMsgRedis.setPushFriendUserStatus(uid, 4*3600);
    //                 SendFcmDTO sendFcmDTO = new SendFcmDTO();
    //                 Map<String, String> paramMap = new HashMap<>();
    //                 paramMap.put(FcmMsgTypeConstant.FCM_MESSAGE_ID_KEY, new ObjectId().toString());
    //                 paramMap.put(FcmMsgTypeConstant.FCM_ORIGIN_KEY, FcmMsgTypeConstant.FCM_SYSTEM);
    //                 paramMap.put(FcmMsgTypeConstant.FCM_SUB_TYPE_KEY, FcmMsgTypeConstant.FCM_SUB_4);
    //                 paramMap.put(FcmMsgTypeConstant.FCM_TITLE_KEY, roomData.getName());
    //                 paramMap.put(FcmMsgTypeConstant.STEP_ACTION_TYPE_KEY, FcmMsgTypeConstant.VOICE_ROOM);
    //                 JSONObject jsonObject2 = new JSONObject();
    //                 jsonObject2.put(FcmMsgTypeConstant.ACTION_VALUE_KEY, roomId);
    //                 paramMap.put(FcmMsgTypeConstant.ACTION_CONFIG_KEY, JSONObject.toJSONString(jsonObject2));
    //
    //                 sendFcmDTO.setParamMap(paramMap);
    //                 sendFcmDTO.setToUidSet(new HashSet<>(friendUidList));
    //                 sendFcmDTO.setTitle(roomData.getName());
    //                 sendFcmDTO.setTitleAr(roomData.getName());
    //                 sendFcmDTO.setBody(String.format("Your friend %s is online!", actorData.getName()));
    //                 sendFcmDTO.setBodyAr(String.format("صديقك %s اونلاين!", actorData.getName()));
    //                 sendFcmDTO.setImg(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
    //                 iFcmService.sendFcmMsg(sendFcmDTO);
    //                 msgLogger.info("newFriendOnMicPush timeMillis={}",  System.currentTimeMillis() - millis);
    //             }
    //         }
    //     }catch (Exception e){
    //         logger.error("newFriendOnMicPush error:{}", e.getMessage(), e);
    //     }
    // }

}
