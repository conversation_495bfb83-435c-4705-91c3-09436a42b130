package com.quhong.service;

import com.quhong.data.PushMsgData;
import com.quhong.enums.LogType;
import com.quhong.feign.IFcmService;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.FriendsDao;
import com.quhong.mysql.dao.DAUDao;
import com.quhong.redis.FcmMsgRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class UserNoActivePushService{

    private static final Logger logger = LoggerFactory.getLogger(UserNoActivePushService.class);
    private static final Logger msgLogger = LoggerFactory.getLogger(LogType.MESSAGE_LOG);
    private static List<PushMsgData> PUSH_MSG_LIST = new ArrayList<>();
    static {
        PUSH_MSG_LIST.add(new PushMsgData("Hello! user_name", "مرحبًا! user_name", "Your friends are waiting for you! Enter the room today and get a surprise.", "أصدقاؤك في انتظارك! ادخل الغرفة اليوم واحصل على مفاجأة."));
        PUSH_MSG_LIST.add(new PushMsgData("recommend_user_name Just mentioned you!", "recommend_user_name ذكرَك للتو!", "They're all waiting for you to come back and chat, Come see what they've been saying!", "الجميع ينتظر عودتك للدردشة، تعال وشاهد ماذا قالوا!"));
        PUSH_MSG_LIST.add(new PushMsgData("Your voice partner is now online!", "شريكك في الصوت متصل الآن!", "Waiting for you in the room for voice chat. Click to join→", "ينتظرك في الغرفة للدردشة الصوتية. انقر للانضمام←"));
        PUSH_MSG_LIST.add(new PushMsgData("Three new friends followed you this week.", "ثلاثة أصدقاء جدد تابعوك هذا الأسبوع.", "Don't keep them waiting too long! 💬", "لا تجعلهم ينتظرون طويلاً! 💬"));
        PUSH_MSG_LIST.add(new PushMsgData("While you were away, a secret room was discovered...", "أثناء غيابك، تم اكتشاف غرفة سرية...", "Click to view🔍", "انقر لعرض🔍"));
        PUSH_MSG_LIST.add(new PushMsgData("There is an unread voice message from a mystery person.", "هناك رسالة صوتية غير مقروءة من شخص غامض.", "Come and listen to who it is! 🎧", "تعال واستمع من هو! 🎧"));
        PUSH_MSG_LIST.add(new PushMsgData("The room you are following is discussing a topic about you", "الغرفة التي تتابعها تدردش موضوعًا عنك", "Come and take a look👀", "تعال وشاهد بسرعة 👀"));
        PUSH_MSG_LIST.add(new PushMsgData("We miss your voice!", "نفتقد صوتك!", "Tonight at 9, same place — don't miss it~", "الليلة الساعة 9، نفس المكان — لا تفوتها~"));
        PUSH_MSG_LIST.add(new PushMsgData("Hot Topics this week", "المواضيع الرائعة لهذا الأسبوع", "It's the final day of the PK competition, and your opinion is crucial! 🗳️", "هو اليوم الأخير من مسابقة، ورأيك مهم جدًا! 🗳️"));
        PUSH_MSG_LIST.add(new PushMsgData("Based on your preferences, we recommend the new room \"Emotional Radio.\"", "بناءً على تفضيلاتك، نوصي بالغرفة الجديدة \"الراديو العاطفي.\"", "The host's voice is very soothing 🎶", "صوت المُقدّم مريح جداً 🎶"));
    }



    @Resource
    private FcmMsgRedis fcmMsgRedis;
    @Resource
    private ActorDao actorDao;
    @Resource
    private FriendsDao friendsDao;
    @Resource
    private IFcmService iFcmService;
    @Resource
    private DAUDao dauDao;

    public void noActivePush() {
        dauDao.getActiveUserSet(3);

    }


    // private void noActivePush() {
    //     try {
    //         int pushFriendUser = fcmMsgRedis.getPushFriendUserStatus(uid);
    //         if (roomData == null){
    //             return;
    //         }
    //         if(pushFriendUser <= 0){
    //             long millis = System.currentTimeMillis();
    //             List<FriendsData> friendsDataList = friendsDao.findAllFriend(uid, false);
    //             int currentTime = DateHelper.getNowSeconds();
    //             int lastSevenTime = currentTime - 7 * 86400;
    //             List<String> friendUidList = friendsDataList.stream().filter(item -> item.getCtime() >= lastSevenTime).map(friend -> uid.equals(friend.getUidSecond()) ? friend.getUidFirst() : friend.getUidSecond()).collect(Collectors.toList());
    //             if (!CollectionUtils.isEmpty(friendUidList)){
    //                 fcmMsgRedis.setPushFriendUserStatus(uid, 4*3600);
    //                 SendFcmDTO sendFcmDTO = new SendFcmDTO();
    //                 Map<String, String> paramMap = new HashMap<>();
    //                 paramMap.put(FcmMsgTypeConstant.FCM_MESSAGE_ID_KEY, new ObjectId().toString());
    //                 paramMap.put(FcmMsgTypeConstant.FCM_ORIGIN_KEY, FcmMsgTypeConstant.FCM_SYSTEM);
    //                 paramMap.put(FcmMsgTypeConstant.FCM_SUB_TYPE_KEY, FcmMsgTypeConstant.FCM_SUB_4);
    //                 paramMap.put(FcmMsgTypeConstant.FCM_TITLE_KEY, roomData.getName());
    //                 paramMap.put(FcmMsgTypeConstant.STEP_ACTION_TYPE_KEY, FcmMsgTypeConstant.VOICE_ROOM);
    //                 JSONObject jsonObject2 = new JSONObject();
    //                 jsonObject2.put(FcmMsgTypeConstant.ACTION_VALUE_KEY, roomId);
    //                 paramMap.put(FcmMsgTypeConstant.ACTION_CONFIG_KEY, JSONObject.toJSONString(jsonObject2));
    //
    //                 sendFcmDTO.setParamMap(paramMap);
    //                 sendFcmDTO.setToUidSet(new HashSet<>(friendUidList));
    //                 sendFcmDTO.setTitle(roomData.getName());
    //                 sendFcmDTO.setTitleAr(roomData.getName());
    //                 sendFcmDTO.setBody(String.format("Your friend %s is online!", actorData.getName()));
    //                 sendFcmDTO.setBodyAr(String.format("صديقك %s اونلاين!", actorData.getName()));
    //                 sendFcmDTO.setImg(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
    //                 iFcmService.sendFcmMsg(sendFcmDTO);
    //                 msgLogger.info("newFriendOnMicPush timeMillis={}",  System.currentTimeMillis() - millis);
    //             }
    //         }
    //     }catch (Exception e){
    //         logger.error("newFriendOnMicPush error:{}", e.getMessage(), e);
    //     }
    // }

}
