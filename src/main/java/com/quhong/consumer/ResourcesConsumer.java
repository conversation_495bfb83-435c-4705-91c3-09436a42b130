package com.quhong.consumer;

import com.alibaba.fastjson.JSON;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.enums.ApiResult;
import com.quhong.monitor.MonitorSender;
import com.quhong.mq.MqItemConstant;
import com.quhong.service.ResourcesService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;

@Component
public class ResourcesConsumer {
    private static final Logger logger = LoggerFactory.getLogger(ResourcesConsumer.class);
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private ResourcesService resourcesService;
    @Resource
    private RabbitTemplate rabbitTemplate;
    private String warnTime = "";

    /**
     * 异步资源处理，消息需要手动确认
     */
    @RabbitListener(containerFactory = "resourcesListenerFactory", queuesToDeclare = @Queue(MqItemConstant.HANDLE_RESOURCES))
    public void handleMessage(Channel channel, Message message) {
        try {
            String json = new String(message.getBody());
            ResourcesDTO resourcesDto = JSON.parseObject(json, ResourcesDTO.class);
            if (!resourcesDto.isParmsValid()) {
                logger.error("message invalid. message={}", json);
                return;
            }
            int handleTime = DateHelper.getNowSeconds() - resourcesDto.getmTime();
            logger.info("receive rabbit mq message, message body={} handleTime={}", json, handleTime);

            String desc = ServerConfig.isProduct() ? "正式服ustar_data_resources" : "测试服ustar_data_resources";
            if (handleTime > 10) {
                logger.error("handle message cost too long costSeconds={}", handleTime);
                String hourTime = DateHelper.ARABIAN.formatTimeInDay(new Date());
                if (!warnTime.equals(hourTime)) {
                    warnTime = hourTime;
                    String warnDetail = "处理时长：" + handleTime + "秒";
                    monitorSender.info("ustar_java_exception", desc + "资源处理超时", warnDetail);
                }
            }
            ApiResult<String> apiResult = resourcesService.dealRes(resourcesDto,true);
            if (resourcesDto.getCancelWarn() <= 0 && apiResult.isError()) {
                logger.error("deal res error, message={} errorResult={}", json, apiResult.getCode().getMsg());
                monitorSender.info("ustar_java_exception", desc + "资源处理异常", "errorResult：" + apiResult.getCode().getMsg());
            }
        } catch (Exception e) {
            logger.error("handle message error. message body={}", new String(message.getBody()), e);
            String desc = ServerConfig.isProduct() ? "正式服ustar_data_resources" : "测试服ustar_data_resources";
            monitorSender.info("ustar_java_exception", desc + "资源消息处理异常", "msg：" + e.getMessage());
        } finally {
            try {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            } catch (IOException e) {
                logger.error("message ack error message={}", new String(message.getBody()), e);
            }
        }
    }
}
