package com.quhong.controllers;

import com.quhong.data.dto.StoreDTO;
import com.quhong.data.vo.RoomLockVO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.WebController;
import com.quhong.service.RoomLockService;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 房间锁
 *
 * <AUTHOR>
 * @date 2022/9/20
 */
@RestController
@RequestMapping(value = "${baseUrl}")
public class RoomLockController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(RoomLockController.class);

    @Resource
    private RoomLockService roomLockService;

    /**
     * 房间锁列表
     */
    @RequestMapping("new_store/room/lock/list")
    public String getRoomLockList(HttpServletRequest request) {
        long millis = System.currentTimeMillis();
        StoreDTO req = RequestUtils.getSendData(request, StoreDTO.class);
        RoomLockVO vo = roomLockService.getRoomLockList(req);
        logger.info("get room lock list. uid={} requestId={} timeMillis={}", req.getUid(), req.getRequestId(), System.currentTimeMillis() - millis);
        return createResult(req, HttpCode.SUCCESS, vo);
    }
}
