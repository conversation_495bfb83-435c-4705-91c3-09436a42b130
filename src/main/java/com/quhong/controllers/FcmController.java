package com.quhong.controllers;

import com.quhong.datas.HttpResult;
import com.quhong.service.FCMPushService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/fcm/")
public class FcmController {

    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private FCMPushService fcmPushService;

    /**
     * fcm测试推送
     */
    @RequestMapping("/fcmMessagePush")
    public HttpResult<?> fcmMessagePush(@RequestParam String userId, @RequestParam(defaultValue = "0") int actionType, @RequestParam(defaultValue = "") String actionValue) {
        logger.info("fcmMessagePush. userId={} actionType={}, actionValue={}", userId, actionType, actionValue);
        fcmPushService.fcmMessagePush(userId, actionType, actionValue);
        return HttpResult.getOk();
    }

}
