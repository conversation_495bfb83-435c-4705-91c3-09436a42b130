package com.quhong.constant;


import com.quhong.core.config.ServerConfig;
import com.quhong.enums.BaseDataResourcesConstant;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class DataResourcesConstant extends BaseDataResourcesConstant {

    public static final String MIC_CHANGE_PATH = "mic_change/to_all";

    public static final int BASE_EXIPRE_START_TIME = 10000; // 默认开始的时间偏移点，不能按大于0，可能有脏数据

    public static final String EXIPRE_DESC = "resources expire"; // 资源过期
    public static final String EXIPRE_NOTE_DESC = "resources expire note"; // 资源过期通知
    public static final String EXIPRE_USE_DESC = "resources use expire"; // 按使用次数资源过期
    public static final String EXIPRE_USE_REDUCE_DESC = "resources use reduce"; // 减少按使用次数资源的次数

    public static final int MIN_VIP_GET_BUDDLE = 5; //最小可获得vip气泡等级
    public static final int MIN_VIP_GET_MIC = 3; //最小可获得vip麦位等级
    public static final int MIN_VIP_GET_ROOM_LOCK = 1; //最小可获得房间锁等级

    public static List<Integer> VIP_JOIN_LIST = Arrays.asList(1, 2, 3, 4, 5, 6, 96, 97, 98, 9, 22);
    public static List<Integer> VIP_BUDDLE_LIST = Arrays.asList(5, 6, 30, 31, 71);
    public static List<Integer> VIP_MIC_LIST = Arrays.asList(3, 4, 5, 6, 186, 187, 188, 334, 355);

    public static final int GAIN_TYPE_USE_MIN_JOIN_ID = 100; // 最小的按使用次数的动画id
    public static final int ITEM_TYPE_NOT_FOUND_BASE_NUM = 1000; //

    public static Map<Integer, Integer> RES_TYPE_MAP_TA_TYPE = new HashMap<>();

    public static final String RESOURCES_TOPIC_QUEUE = "resources_topic_msg_queue";

    @PostConstruct
    public void postInit() {
        if (ServerConfig.isNotProduct()) {
            VIP_JOIN_LIST = Arrays.asList(1, 2, 3, 4, 5, 6, 83, 84, 85, 9, 23);
            VIP_BUDDLE_LIST = Arrays.asList(5, 6, 34, 35, 70);
            VIP_MIC_LIST = Arrays.asList(3, 4, 5, 6, 215, 216, 217, 387, 411);
        }
        RES_TYPE_MAP_TA_TYPE.put(TYPE_BADGE, 1);
        RES_TYPE_MAP_TA_TYPE.put(TYPE_MIC, 3);
        RES_TYPE_MAP_TA_TYPE.put(TYPE_BUDDLE, 4);
        RES_TYPE_MAP_TA_TYPE.put(TYPE_RIPPLE, 5);
        RES_TYPE_MAP_TA_TYPE.put(TYPE_RIDE, 6);
        RES_TYPE_MAP_TA_TYPE.put(TYPE_BAG_GIFT, 7);
        RES_TYPE_MAP_TA_TYPE.put(TYPE_FLOAT_SCREEN, 8);
        RES_TYPE_MAP_TA_TYPE.put(TYPE_MINE_BACKGROUND, 9);
        RES_TYPE_MAP_TA_TYPE.put(TYPE_BEAUTIFUL_RID, 10);
        RES_TYPE_MAP_TA_TYPE.put(TYPE_ENTRY_EFFECT, 11);
        RES_TYPE_MAP_TA_TYPE.put(TYPE_HONOR_TITLE, 12);
    }
}
