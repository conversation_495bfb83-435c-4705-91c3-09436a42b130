package com.quhong.mongo.data;

import com.quhong.mongo.dao.FingerGuessDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * <AUTHOR>
 * @date 2022/11/3
 */
@Document(collection = FingerGuessDao.TABLE_NAME)
public class FingerGuessData {

    @Id
    private ObjectId _id;

    /**
     * 发起者
     */
    private String uid;

    /**
     * 0--剪刀， 1--石头， 2--布
     */
    @Field("uid_gesture")
    private int uidGesture;

    /**
     * 0--剪刀， 1--石头， 2--布
     */
    @Field("aid_gesture")
    private int aidGesture;

    /**
     * 创建游戏时的房间
     */
    @Field("creator_room_id")
    private String creatorRoomId;

    /**
     * 加入游戏的房间
     */
    @Field("rec_room_id")
    private String recRoomId;

    /**
     * 加入者
     */
    private String aid;

    /**
     * 获胜者
     */
    private String winner;

    /**
     * 礼物id
     */
    private int gid;

    /**
     * 礼物icon
     */
    @Field("g_icon")
    private String gIcon;

    /**
     * 礼物的钻石数
     */
    @Field("g_beans")
    private int gBeans;

    /**
     * 创建时间
     */
    @Field("c_time")
    private int cTime;

    /**
     * 结束时间
     */
    @Field("end_time")
    private int endTime;

    /**
     * 状态： 0等待加入， 1代表2个人玩了之后的正常结束， 2代表超时系统结束
     */
    private int status;

    /**
     * 是否机器人创建 1机器人
     */
    private int robot;

    public FingerGuessData() {
    }

    public FingerGuessData(String uid, int uidGesture, int gid, String gIcon, int gBeans, int cTime, String creatorRoomId, int robot) {
        this.uid = uid;
        this.uidGesture = uidGesture;
        this.aidGesture = 0;
        this.creatorRoomId = creatorRoomId;
        this.recRoomId = "";
        this.aid = "";
        this.winner = "";
        this.gid = gid;
        this.gIcon = gIcon;
        this.gBeans = gBeans;
        this.cTime = cTime;
        this.endTime = 0;
        this.status = 0;
        this.robot = robot;
    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getUidGesture() {
        return uidGesture;
    }

    public void setUidGesture(int uidGesture) {
        this.uidGesture = uidGesture;
    }

    public int getAidGesture() {
        return aidGesture;
    }

    public void setAidGesture(int aidGesture) {
        this.aidGesture = aidGesture;
    }

    public String getCreatorRoomId() {
        return creatorRoomId;
    }

    public void setCreatorRoomId(String creatorRoomId) {
        this.creatorRoomId = creatorRoomId;
    }

    public String getRecRoomId() {
        return recRoomId;
    }

    public void setRecRoomId(String recRoomId) {
        this.recRoomId = recRoomId;
    }

    public String getAid() {
        return aid;
    }

    public void setAid(String aid) {
        this.aid = aid;
    }

    public String getWinner() {
        return winner;
    }

    public void setWinner(String winner) {
        this.winner = winner;
    }

    public int getGid() {
        return gid;
    }

    public void setGid(int gid) {
        this.gid = gid;
    }

    public String getgIcon() {
        return gIcon;
    }

    public void setgIcon(String gIcon) {
        this.gIcon = gIcon;
    }

    public int getgBeans() {
        return gBeans;
    }

    public void setgBeans(int gBeans) {
        this.gBeans = gBeans;
    }

    public int getcTime() {
        return cTime;
    }

    public void setcTime(int cTime) {
        this.cTime = cTime;
    }

    public int getEndTime() {
        return endTime;
    }

    public void setEndTime(int endTime) {
        this.endTime = endTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getRobot() {
        return robot;
    }

    public void setRobot(int robot) {
        this.robot = robot;
    }
}
