package com.quhong.mongo.data;

import com.quhong.core.utils.DateHelper;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2022/9/30
 */
@Document(collection = "admin_tn_device_log")
public class AdminTnDeviceLogData {

    @Id
    private ObjectId _id;

    /**
     * 用户uid
     */
    private String uid;

    /**
     * 管理员uid
     */
    private String op_uid;

    private String tn_id;

    private int s_type;

    private int o_type;

    /**
     * 1 封禁 2 解封全部  51-57 系统相关解封
     */
    private int c_type;

    /**
     * 操作备注
     */
    private String desc;

    /**
     * 封禁天数
     */
    private int block_term;

    /**
     * 解封时间
     */
    private int release_at;

    /**
     * 被封禁操作当前是否有效，1 有效 2 无效
     */
    private int status;

    /**
     * uid是否充值用户 0: 否  1: 是
     */
    private int recharge_user;

    /**
     * 创建时间
     */
    private int c_time;

    public AdminTnDeviceLogData() {
    }

    public AdminTnDeviceLogData(String uid, String op_uid, String tn_id, int s_type, int o_type, int c_type,
                                String desc, int block_term, int release_at) {
        this.uid = uid;
        this.op_uid = op_uid;
        this.tn_id = tn_id;
        this.s_type = s_type;
        this.o_type = o_type;
        this.c_type = c_type;
        this.desc = desc;
        this.block_term = block_term;
        this.release_at = release_at;
        this.c_time = DateHelper.getNowSeconds();
    }

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getOp_uid() {
        return op_uid;
    }

    public void setOp_uid(String op_uid) {
        this.op_uid = op_uid;
    }

    public String getTn_id() {
        return tn_id;
    }

    public void setTn_id(String tn_id) {
        this.tn_id = tn_id;
    }

    public int getS_type() {
        return s_type;
    }

    public void setS_type(int s_type) {
        this.s_type = s_type;
    }

    public int getO_type() {
        return o_type;
    }

    public void setO_type(int o_type) {
        this.o_type = o_type;
    }

    public int getC_type() {
        return c_type;
    }

    public void setC_type(int c_type) {
        this.c_type = c_type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public int getBlock_term() {
        return block_term;
    }

    public void setBlock_term(int block_term) {
        this.block_term = block_term;
    }

    public int getRelease_at() {
        return release_at;
    }

    public void setRelease_at(int release_at) {
        this.release_at = release_at;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getRecharge_user() {
        return recharge_user;
    }

    public void setRecharge_user(int recharge_user) {
        this.recharge_user = recharge_user;
    }

    public int getC_time() {
        return c_time;
    }

    public void setC_time(int c_time) {
        this.c_time = c_time;
    }
}
