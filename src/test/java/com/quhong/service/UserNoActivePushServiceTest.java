package com.quhong.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import javax.annotation.Resource;

/**
 * 不活跃用户推送服务测试
 */
@SpringBootTest
@SpringJUnitConfig
public class UserNoActivePushServiceTest {

    @Resource
    private UserNoActivePushService userNoActivePushService;

    @Test
    public void testNoActivePush() {
        // 测试不活跃用户推送方法
        userNoActivePushService.noActivePush();
    }
}
