package com.quhong.msg;

import com.quhong.core.msg.IProto;
import com.quhong.proto.YoustarProtoBase;

public class ProtoHeader implements IProto<YoustarProtoBase.MsgHeader> {
    private long msgId; //消息id，用于消息确认和去重
    private String fromUid = ""; //发送者uid
    private String token = ""; //token
    private String roomID = ""; //房间ID
    private boolean responseAck; //是否需要客户端回包
    private String versionName = ""; //客户端版本
    private long requestTime; //请求时间

    public ProtoHeader() {

    }

    public void doFromBody(YoustarProtoBase.MsgHeader proto) {
        this.msgId = proto.getMsgId();
        this.fromUid = proto.getFromUid();
        this.token = proto.getToken();
        this.roomID = proto.getRoomID();
        this.versionName = proto.getVersionName();
        this.responseAck = proto.getResponseAck();
        this.requestTime = proto.getRequestTime();
    }

    public YoustarProtoBase.MsgHeader.Builder doToBody() {
        YoustarProtoBase.MsgHeader.Builder builder = YoustarProtoBase.MsgHeader.newBuilder();
        builder.setMsgId(msgId);
        builder.setFromUid(fromUid == null ? "" : fromUid);
        builder.setToken(token == null ? "" : token);
        builder.setRoomID(roomID == null ? "" : roomID);
        builder.setResponseAck(responseAck);
        builder.setVersionName(versionName == null ? "" : versionName);
        builder.setRequestTime(requestTime);
        return builder;
    }

    public long getMsgId() {
        return msgId;
    }

    public void setMsgId(long msgId) {
        this.msgId = msgId;
    }

    public String getFromUid() {
        return fromUid;
    }

    public void setFromUid(String fromUid) {
        this.fromUid = fromUid;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getRoomID() {
        return roomID;
    }

    public void setRoomID(String roomID) {
        this.roomID = roomID;
    }

    public boolean isResponseAck() {
        return responseAck;
    }

    public void setResponseAck(boolean responseAck) {
        this.responseAck = responseAck;
    }

    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public long getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(long requestTime) {
        this.requestTime = requestTime;
    }
}
