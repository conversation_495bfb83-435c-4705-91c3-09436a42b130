package com.quhong.analysis;

/**
 * moment_interact_log
 * 点赞动态后上报：uid，moment_id（动态id），moment_uid（发动态的用户uid），moment_interact_type=1，ctime
 * 评论动态后上报：uid，moment_id（动态id），moment_uid（发动态的用户uid），moment_interact_type=2，ctime
 * 点赞动态里的评论后上报：uid，moment_id（动态id），moment_uid（发动态的用户uid），moment_interact_type=3，ctime
 * 回复动态里的评论后上报：uid，moment_id（动态id），moment_uid（发动态的用户uid），moment_interact_type=4，ctime
 */
public class MomentInteractLogEvent extends UserEvent {
    private String moment_id; // 动态id
    private String moment_uid; // 发动态的用户uid
    // 1点赞动态后上报 2评论动态后上报 3点赞动态里的评论后上报 4回复动态里的评论后上报 5发送礼物打赏后上报
    // 6 取消动态的点赞  7 取消动态里的评论点赞 8 分享贴子给朋友
    private int moment_interact_type;
    private int ctime;

    private Integer share_nums; // 分享人数 分享帖子场景下有值
    private int is_topic; // 0 否 1 是
    private String topic_name;
    private Integer topic_id;
    private String topic_create_uid;

    @Override
    public String getEventName() {
        return "moment_interact_log";
    }

    @Override
    public int getEventTime() {
        return 0;
    }

    public String getMoment_id() {
        return moment_id;
    }

    public void setMoment_id(String moment_id) {
        this.moment_id = moment_id;
    }

    public String getMoment_uid() {
        return moment_uid;
    }

    public void setMoment_uid(String moment_uid) {
        this.moment_uid = moment_uid;
    }

    public int getMoment_interact_type() {
        return moment_interact_type;
    }

    public void setMoment_interact_type(int moment_interact_type) {
        this.moment_interact_type = moment_interact_type;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public Integer getShare_nums() {
        return share_nums;
    }

    public void setShare_nums(Integer share_nums) {
        this.share_nums = share_nums;
    }

    public int getIs_topic() {
        return is_topic;
    }

    public void setIs_topic(int is_topic) {
        this.is_topic = is_topic;
    }

    public String getTopic_name() {
        return topic_name;
    }

    public void setTopic_name(String topic_name) {
        this.topic_name = topic_name;
    }

    public Integer getTopic_id() {
        return topic_id;
    }

    public void setTopic_id(Integer topic_id) {
        this.topic_id = topic_id;
    }

    public String getTopic_create_uid() {
        return topic_create_uid;
    }

    public void setTopic_create_uid(String topic_create_uid) {
        this.topic_create_uid = topic_create_uid;
    }
}
