package com.quhong.analysis;


public class AccountLogEvent extends UserEvent {
    private String distinctId;
    private int ctime;
    private int account_action; //

    @Override
    public String getEventName() {
        return "account_log";
    }

    @Override
    public int getEventTime() {
        return ctime;
    }

    public String getDistinctId() {
        return distinctId;
    }

    public void setDistinctId(String distinctId) {
        this.distinctId = distinctId;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }

    public int getAccount_action() {
        return account_action;
    }

    public void setAccount_action(int account_action) {
        this.account_action = account_action;
    }
}
