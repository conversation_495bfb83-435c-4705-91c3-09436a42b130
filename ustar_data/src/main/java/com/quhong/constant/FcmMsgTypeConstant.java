package com.quhong.constant;

/**
 * fcm消息跳转Type
 */
public class FcmMsgTypeConstant {

    public static final String LIVE_ROOM = "8";                  // 通用跳转房间
    public static final String PRIVATE_MSG = "24";               // 私信通知, 跳转私信列表

    public static final String STEP_ACTION_TYPE_KEY = "actionType";
    public static final String ACTION_CONFIG_KEY = "actionConfig"; // 值为json对象

    /**
     * type为8-15有值
     * 8 为话题id
     * 9 为朋友圈id
     * 10 为房间id
     * 11 为url
     * 12 为活动详情id
     * 14 指定用户uid
     * 15 指定私聊对象的uid
     */
    public static final String ACTION_VALUE_KEY = "actionValue"; // actionConfig内的key，值为相应type指定的值

    /**
     * type为15有值
     * 15 指定私聊对象的head
     *
     */
    public static final String ACTION_HEAD_KEY = "actionHead"; // actionConfig内的key，值为相应type指定对象的头像链接

    public static final String SOCIAL_HOME = "1";  // 应用social页
    public static final String ROOM_ALL_HOT = "2";// 应用room页-All-hot
    public static final String ROOM_ALL_NEW = "3";// 应用room页-All-new
    public static final String ROOM_DISCOVER = "4";// 应用room页-Discover
    public static final String ROOM_DISCOVER_EVENT = "5";// 应用room页-room-Discover-event
    public static final String ROOM_GAME_HOT = "6";// 应用room页-Game-hot
    public static final String MOMENT_FEATURED = "7";// 应用moment-Featured
    public static final String MOMENT_TOPIC = "8";// 指定朋友圈话题
    public static final String MOMENT_DETAIL = "9";// 指定朋友圈动态
    public static final String VOICE_ROOM = "10";// 指定语音房
    public static final String WEB_URL = "11";// 跳转web
    public static final String ROOM_DETAIL_EVENT = "12";//指定房间活动详情跳转
    public static final String STEP_PRIVATE_MSG = "13";// 跳转私信列表
    public static final String USER_DETAIL_MSG = "14";// 跳转个人主页
    public static final String PRIVATE_DETAIL_MSG = "15";// 跳转私聊页
    public static final String OFFICIAL_MSG = "16";// 跳转官方消息列表页
    public static final String ACTIVITY_MSG = "17";// 跳转消息活动列表页
    public static final String EDIT_PROFILE = "18";  // 跳转个人资料编辑页



    public static final String FCM_MESSAGE_ID_KEY = "fcm_message_id";  // fcm消息ID
    public static final String FCM_TITLE_KEY = "fcm_title";            // 消息标题

    /**
     * 推送来源
     */
    public static final String FCM_ORIGIN_KEY = "fcm_type";            // 消息来源
    public static final String FCM_OPERATION = "1";                    // 运营平台推送
    public static final String FCM_SYSTEM = "2";                       // 系统触发


    /**
     * 推送子类型
     */
    public static final String FCM_SUB_TYPE_KEY = "fcm_subtype";        // fcm子类型
    public static final String FCM_SUB_1 = "100-1";                     // 房间推送-推送给关注房间者
    public static final String FCM_SUB_2 = "100-2";                     // 房间推送-Hot列表热门房间推荐
    public static final String FCM_SUB_3 = "100-2";                     // 房间推送-房间活动推荐
    public static final String FCM_SUB_4 = "101-1";                     // 用户推荐-好友上麦
    public static final String FCM_SUB_5 = "101-1";                     // 用户推荐-最近30天注册女性用户上麦
    public static final String FCM_SUB_6 = "102-1";                     // 私信消息推送-好友发私信消息


}
