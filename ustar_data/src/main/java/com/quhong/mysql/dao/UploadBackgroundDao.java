package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.quhong.mysql.data.UploadBackgroundData;
import com.quhong.mysql.mapper.ustar.UploadBackgroundMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/20
 */
@Component
public class UploadBackgroundDao {

    @Resource
    private UploadBackgroundMapper uploadBackgroundMapper;

    public void insert(UploadBackgroundData data) {
        uploadBackgroundMapper.insert(data);
    }

    public List<UploadBackgroundData> selectList(String uid) {
        QueryWrapper<UploadBackgroundData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.eq("uid", uid);
        queryWrapper.orderByDesc("ctime");
        return uploadBackgroundMapper.selectList(queryWrapper);
    }

    public Integer selectCount(String uid) {
        QueryWrapper<UploadBackgroundData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.eq("uid", uid);
        return uploadBackgroundMapper.selectCount(queryWrapper);
    }

    public List<UploadBackgroundData> selectPage(String uid, int page, int pageSize) {
        QueryWrapper<UploadBackgroundData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.eq("uid", uid);
        queryWrapper.orderByDesc("ctime");
        IPage<UploadBackgroundData> iPage = new Page<>(page, pageSize);
        iPage = uploadBackgroundMapper.selectPage(iPage, queryWrapper);
        return iPage.getRecords();
    }

    public List<UploadBackgroundData> selectPage(String uid, String aid, int page, int pageSize) {
        QueryWrapper<UploadBackgroundData> queryWrapper = new QueryWrapper<>();
        queryWrapper.and(wrapper -> wrapper.eq("uid", uid).or().eq("uid", aid));
        queryWrapper.eq("status", 1);
        queryWrapper.orderByDesc("ctime");
        IPage<UploadBackgroundData> iPage = new Page<>(page, pageSize);
        iPage = uploadBackgroundMapper.selectPage(iPage, queryWrapper);
        return iPage.getRecords();
    }

    public UploadBackgroundData selectOne(int id) {
        QueryWrapper<UploadBackgroundData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.eq("id", id);
        return uploadBackgroundMapper.selectOne(queryWrapper);
    }

    public UploadBackgroundData selectOne(String uid, int id) {
        QueryWrapper<UploadBackgroundData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.eq("id", id);
        queryWrapper.eq("uid", uid);
        return uploadBackgroundMapper.selectOne(queryWrapper);
    }

    public UploadBackgroundData selectOne(String uid, String aid, int id) {
        QueryWrapper<UploadBackgroundData> queryWrapper = new QueryWrapper<>();
        queryWrapper.and(wrapper -> wrapper.eq("uid", uid).or().eq("uid", aid));
        queryWrapper.eq("status", 1);
        queryWrapper.eq("id", id);
        return uploadBackgroundMapper.selectOne(queryWrapper);
    }

    public void deleteById(Integer id) {
        uploadBackgroundMapper.deleteById(id);
    }

    public UploadBackgroundData selectOneByUid(String uid) {
        QueryWrapper<UploadBackgroundData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.eq("uid", uid);
        queryWrapper.orderByDesc("ctime");
        queryWrapper.last("limit 1");
        return uploadBackgroundMapper.selectOne(queryWrapper);
    }
}
