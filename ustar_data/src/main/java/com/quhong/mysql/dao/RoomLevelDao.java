package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.RoomLevelData;
import com.quhong.mysql.mapper.ustar.RoomLevelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Lazy
@Component
public class RoomLevelDao extends ServiceImpl<RoomLevelMapper, RoomLevelData> {
    private static final Logger logger = LoggerFactory.getLogger(RoomLevelDao.class);

    public RoomLevelData insertMy(RoomLevelData data) {
        try {
            baseMapper.insertMy(data);
            return data;
        } catch (Exception e) {
            logger.error("insertMy error msg={}", e.getMessage(), e);
            return null;
        }
    }

    public void updateByRoomId(RoomLevelData data) {
            LambdaUpdateWrapper<RoomLevelData> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            lambdaUpdateWrapper.eq(RoomLevelData::getRoomId, data.getRoomId())
                    .set(RoomLevelData::getLevel, data.getLevel())
                    .set(RoomLevelData::getExp, data.getExp())
                    .set(RoomLevelData::getMtime, DateHelper.getNowSeconds());
            update(lambdaUpdateWrapper);

    }

    public RoomLevelData findOne(String roomId) {
        try {
            QueryWrapper<RoomLevelData> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("room_id", roomId);
            return getOne(queryWrapper);
        } catch (Exception e) {
            logger.error("findOne roomId={} msg={}", roomId, e.getMessage(), e);
            return null;
        }
    }

}
