package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quhong.mysql.data.QuizCheckpointConfigData;
import com.quhong.mysql.mapper.ustar.QuizCheckpointConfigMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/27
 */
@Component
public class QuizCheckpointConfigDao {

    @Resource
    private QuizCheckpointConfigMapper quizCheckpointConfigMapper;

    public void insert(QuizCheckpointConfigData data) {
        quizCheckpointConfigMapper.insert(data);
    }

    public List<QuizCheckpointConfigData> selectByTemplateId(int templateId) {
        QueryWrapper<QuizCheckpointConfigData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id", templateId);
        queryWrapper.orderByAsc("checkpoint_no");
        return quizCheckpointConfigMapper.selectList(queryWrapper);
    }

    public void deleteByTemplateId(int templateId) {
        QueryWrapper<QuizCheckpointConfigData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id", templateId);
        quizCheckpointConfigMapper.delete(queryWrapper);
    }

    public QuizCheckpointConfigData selectOne(Integer templateId, Integer checkpointNo) {
        QueryWrapper<QuizCheckpointConfigData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id", templateId);
        queryWrapper.eq("checkpoint_no", checkpointNo);
        return quizCheckpointConfigMapper.selectOne(queryWrapper);
    }

    public int getCountByTemplateId(Integer templateId) {
        QueryWrapper<QuizCheckpointConfigData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_id", templateId);
        return quizCheckpointConfigMapper.selectCount(queryWrapper);
    }
}
