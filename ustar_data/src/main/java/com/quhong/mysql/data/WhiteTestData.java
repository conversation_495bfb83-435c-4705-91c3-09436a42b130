package com.quhong.mysql.data;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_white_test")
public class WhiteTestData {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private String whiteId;    // 白名单id  uid或room_id
    private String tnId;
    private int type;          // 加入方式  0: 以账号rid加入用户白名单  1: 以账号tnId加入用户白名单 whiteId tnId都有值  2: 以账号rid加入房间白名单
    private String belong;     // 账号所属
    private Integer ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getWhiteId() {
        return whiteId;
    }

    public void setWhiteId(String whiteId) {
        this.whiteId = whiteId;
    }

    public String getTnId() {
        return tnId;
    }

    public void setTnId(String tnId) {
        this.tnId = tnId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getBelong() {
        return belong;
    }

    public void setBelong(String belong) {
        this.belong = belong;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
