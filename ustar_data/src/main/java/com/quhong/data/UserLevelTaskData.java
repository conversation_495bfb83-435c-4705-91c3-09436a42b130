package com.quhong.data;

public class UserLevelTaskData {

    private String uid;
    private String item;
    private String handle_id;
    private int value;
    private String date_str;
    private long time_stamp;
    private int os;
    private int versioncode;

    public UserLevelTaskData() {
    }

    public UserLevelTaskData(String uid, String item) {
        this.uid = uid;
        this.item = item;
    }

    public UserLevelTaskData(String uid, String item, String handle_id) {
        this.uid = uid;
        this.item = item;
        this.handle_id = handle_id;
    }

    public UserLevelTaskData(String uid, String item, int value) {
        this.uid = uid;
        this.item = item;
        this.value = value;
    }

    public UserLevelTaskData(String uid, String item, String handle_id, long time_stamp) {
        this.uid = uid;
        this.item = item;
        this.handle_id = handle_id;
        this.time_stamp = time_stamp;
    }

    public UserLevelTaskData(String uid, String item, int os, int versioncode) {
        this.uid = uid;
        this.item = item;
        this.os = os;
        this.versioncode = versioncode;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getItem() {
        return item;
    }

    public void setItem(String item) {
        this.item = item;
    }

    public String getHandle_id() {
        return handle_id;
    }

    public void setHandle_id(String handle_id) {
        this.handle_id = handle_id;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getDate_str() {
        return date_str;
    }

    public void setDate_str(String date_str) {
        this.date_str = date_str;
    }

    public long getTime_stamp() {
        return time_stamp;
    }

    public void setTime_stamp(long time_stamp) {
        this.time_stamp = time_stamp;
    }

    public int getOs() {
        return os;
    }

    public void setOs(int os) {
        this.os = os;
    }

    public int getVersioncode() {
        return versioncode;
    }

    public void setVersioncode(int versioncode) {
        this.versioncode = versioncode;
    }

    @Override
    public String toString() {
        return "UserLevelTaskData{" +
                "uid='" + uid + '\'' +
                ", item='" + item + '\'' +
                ", handle_id='" + handle_id + '\'' +
                ", value=" + value +
                ", date_str='" + date_str + '\'' +
                ", time_stamp=" + time_stamp +
                ", os=" + os +
                ", versioncode=" + versioncode +
                '}';
    }
}
