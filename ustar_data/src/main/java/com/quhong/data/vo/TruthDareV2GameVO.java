package com.quhong.data.vo;

import com.quhong.data.TruthOrDareV2Info;
import com.quhong.data.vo.OptMicThemeVO;

import java.util.List;

public class TruthDareV2GameVO {

    // 话题相关返回
    private List<TruthDareTopicVO> myTopicList;
    private List<TruthDareTopicVO> truthTopicList;
    private List<TruthDareTopicVO> dareTopicList;
    private String gameRuleUrl;

    // 创建游戏返回
    private TruthOrDareV2Info gameInfo;
    private OptMicThemeVO micThemeInfo;

    // 开始游戏返回选中用户
    private TruthOrDareV2Info.SelectedUserInfo selectedUser;

    // 选择话题类型随机返回话题内容
    private TruthDareTopicVO selectedTopic;

    public List<TruthDareTopicVO> getMyTopicList() {
        return myTopicList;
    }

    public void setMyTopicList(List<TruthDareTopicVO> myTopicList) {
        this.myTopicList = myTopicList;
    }

    public List<TruthDareTopicVO> getTruthTopicList() {
        return truthTopicList;
    }

    public void setTruthTopicList(List<TruthDareTopicVO> truthTopicList) {
        this.truthTopicList = truthTopicList;
    }

    public List<TruthDareTopicVO> getDareTopicList() {
        return dareTopicList;
    }

    public void setDareTopicList(List<TruthDareTopicVO> dareTopicList) {
        this.dareTopicList = dareTopicList;
    }

    public String getGameRuleUrl() {
        return gameRuleUrl;
    }

    public void setGameRuleUrl(String gameRuleUrl) {
        this.gameRuleUrl = gameRuleUrl;
    }

    public TruthOrDareV2Info getGameInfo() {
        return gameInfo;
    }

    public void setGameInfo(TruthOrDareV2Info gameInfo) {
        this.gameInfo = gameInfo;
    }

    public OptMicThemeVO getMicThemeInfo() {
        return micThemeInfo;
    }

    public void setMicThemeInfo(OptMicThemeVO micThemeInfo) {
        this.micThemeInfo = micThemeInfo;
    }

    public TruthOrDareV2Info.SelectedUserInfo getSelectedUser() {
        return selectedUser;
    }

    public void setSelectedUser(TruthOrDareV2Info.SelectedUserInfo selectedUser) {
        this.selectedUser = selectedUser;
    }

    public TruthDareTopicVO getSelectedTopic() {
        return selectedTopic;
    }

    public void setSelectedTopic(TruthDareTopicVO selectedTopic) {
        this.selectedTopic = selectedTopic;
    }
}
