package com.quhong.room.data;

import com.alibaba.fastjson.annotation.JSONField;

public class EnterUserData {
    @JSONField(name = "B")
    private String micFrame; //麦位框
    @JSONField(name = "C")
    private String rippleUrl;//波纹地址
    @JSONField(name = "D")
    private String smallIcon;//进场动画预览图
    @JSONField(name = "E")
    private int joinCartoonId = 0; //进入房间动画id
    @JSONField(name = "F")
    private int sourceType = 0; //资源类型
    @JSONField(name = "G")
    private int joinCartoonType = 0;//进场动画类型   1 vip  2 荣誉 3 商店购买 4 svip 佩戴
    @JSONField(name = "H")
    private long cartoonTimes = 0;//进场动画持续时间 ms
    @JSONField(name = "I")
    private long forbidOnTime = 0; //禁言时间 ms
    @JSONField(name = "J")
    private long micForbidOnTime = 0;//麦位禁止时间 ms
    @JSONField(name = "K")
    private String age; //年龄
    @JSONField(name = "L")
    private int gender; //性别
    @JSONField(name = "M")
    private int role; //角色
    @JSONField(name = "N")
    private String source_url; // 坐骑zip Url
    @JSONField(name = "O")
    private String source_md5; // 坐骑zip MD5值
    @JSONField(name = "P")
    private String entryEffectUrl; // 进场通知url
    @JSONField(name = "Q")
    private String entryEffectUrlAr; // 进场通知url

    public EnterUserData(){

    }

    public String getMicFrame() {
        return micFrame;
    }

    public void setMicFrame(String micFrame) {
        this.micFrame = micFrame;
    }

    public String getRippleUrl() {
        return rippleUrl;
    }

    public void setRippleUrl(String rippleUrl) {
        this.rippleUrl = rippleUrl;
    }

    public String getSmallIcon() {
        return smallIcon;
    }

    public void setSmallIcon(String smallIcon) {
        this.smallIcon = smallIcon;
    }

    public int getJoinCartoonId() {
        return joinCartoonId;
    }

    public void setJoinCartoonId(int joinCartoonId) {
        this.joinCartoonId = joinCartoonId;
    }

    public int getSourceType() {
        return sourceType;
    }

    public void setSourceType(int sourceType) {
        this.sourceType = sourceType;
    }

    public int getJoinCartoonType() {
        return joinCartoonType;
    }

    public void setJoinCartoonType(int joinCartoonType) {
        this.joinCartoonType = joinCartoonType;
    }

    public long getCartoonTimes() {
        return cartoonTimes;
    }

    public void setCartoonTimes(long cartoonTimes) {
        this.cartoonTimes = cartoonTimes;
    }

    public long getForbidOnTime() {
        return forbidOnTime;
    }

    public void setForbidOnTime(long forbidOnTime) {
        this.forbidOnTime = forbidOnTime;
    }

    public long getMicForbidOnTime() {
        return micForbidOnTime;
    }

    public void setMicForbidOnTime(long micForbidOnTime) {
        this.micForbidOnTime = micForbidOnTime;
    }

    public String getAge() {
        return age;
    }

    public void setAge(String age) {
        this.age = age;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public int getRole() {
        return role;
    }

    public void setRole(int role) {
        this.role = role;
    }

    public String getSource_url() {
        return source_url;
    }

    public void setSource_url(String source_url) {
        this.source_url = source_url;
    }

    public String getSource_md5() {
        return source_md5;
    }

    public void setSource_md5(String source_md5) {
        this.source_md5 = source_md5;
    }

    public String getEntryEffectUrl() {
        return entryEffectUrl;
    }

    public void setEntryEffectUrl(String entryEffectUrl) {
        this.entryEffectUrl = entryEffectUrl;
    }

    public String getEntryEffectUrlAr() {
        return entryEffectUrlAr;
    }

    public void setEntryEffectUrlAr(String entryEffectUrlAr) {
        this.entryEffectUrlAr = entryEffectUrlAr;
    }
}
