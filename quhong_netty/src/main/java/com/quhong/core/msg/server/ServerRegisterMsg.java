package com.quhong.core.msg.server;

import com.quhong.core.annotation.Message;
import com.quhong.core.msg.ServerMsg;
import com.quhong.enums.BaseServerCmd;
import com.quhong.server.protobuf.ServerProto;

@Message(cmd = BaseServerCmd.SERVER_REGISTER, isServer = true)
public class ServerRegisterMsg extends ServerMsg {
    private int serverId;
    private String host = "";
    private int port;

    @Override
    protected void doFromBody(byte[] bytes) throws Exception {
        ServerProto.ServerRegister msg = ServerProto.ServerRegister.parseFrom(bytes);
        this.serverId = msg.getServerId();
        this.host = msg.getHost();
        this.port = msg.getPort();
    }

    @Override
    protected byte[] doToBody() throws Exception {
        ServerProto.ServerRegister.Builder builder = ServerProto.ServerRegister.newBuilder();
        builder.setServerId(this.serverId);
        builder.setHost(this.host);
        builder.setPort(this.port);
        return builder.build().toByteArray();
    }

    public int getServerId() {
        return serverId;
    }

    public void setServerId(int serverId) {
        this.serverId = serverId;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }
}
