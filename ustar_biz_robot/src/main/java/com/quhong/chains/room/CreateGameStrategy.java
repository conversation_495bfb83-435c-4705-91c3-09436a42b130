package com.quhong.chains.room;

import com.alibaba.fastjson.JSONObject;
import com.quhong.chains.AbstractRobotStrategy;
import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.SpringUtils;
import com.quhong.executors.http.CreateGameHandler;
import com.quhong.robot.Robot;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.concurrent.ThreadLocalRandom;

public class CreateGameStrategy extends AbstractRobotStrategy {
    private final CreateGameHandler createGameHandler;

    public CreateGameStrategy(Robot robot) {
        super(robot);
        this.needTick = true;
        createGameHandler = SpringUtils.getBean(CreateGameHandler.class);
    }

    @Override
    protected void doDispose() {
        // for override
    }

    @Override
    public void start() {

    }

    @Override
    protected void doOnTick() {
        synchronized (this.robot) {
            if (!StringUtils.isEmpty(robot.getRoomId())) {
                if (!robot.getRobotData().isCreateGameStatus()) {
                    if (DateHelper.getNowSeconds() - robot.getRobotData().getCreateGameTime() > 0) {
                        CreateGameHandler.CreateSudGameDTO dto = new CreateGameHandler.CreateSudGameDTO();
                        dto.setCurrencyType(1);
                        dto.setCurrency(500);
                        // 2ludo 3umo 4crush 5domino 6CarromPool
                        // crush ai太弱，排除
                        int[] gameTypes = {2, 3, 5};
                        int randomIndex = ThreadLocalRandom.current().nextInt(gameTypes.length);
                        dto.setGameType(gameTypes[randomIndex]);
                        dto.setPlayerNumber(4);
                        if (dto.getGameType() == 2) {
                            JSONObject rule = new JSONObject();
                            rule.put("mode", ThreadLocalRandom.current().nextBoolean() ? 1 : 0); // 0快速，1经典
                            rule.put("chessNum", ThreadLocalRandom.current().nextBoolean() ? 2 : 4); // 2对应2颗棋子，4对应4颗棋子
                            rule.put("item", ThreadLocalRandom.current().nextBoolean() ? 1 : 0); // 1要，0不要
                            dto.setRule(rule);
                        } else if (dto.getGameType() == 6) {
                            JSONObject rule = new JSONObject();
                            // 1、2、3 分别表示 Carrom、Freestyle、2v2
                            // 随机创建1、2
                            rule.put("mode_ex", ThreadLocalRandom.current().nextInt(1, 3));
                            dto.setRule(rule);
                        }
                        createGameHandler.createGame(this.robot, dto);
                    }
                }
                if (null != robot.getRobotData().getCreateGameId()
                        && !ObjectUtils.isEmpty(robot.getRoomId())
                        && robot.getRobotData().getStartGameTime() != 0
                        && DateHelper.getNowSeconds() - robot.getRobotData().getStartGameTime() > 0) {
                    createGameHandler.startGame(this.robot);
                }
            }
        }
    }
}
